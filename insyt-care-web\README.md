# Insyt Care Dashboard

A comprehensive healthcare management dashboard built with React, designed for managing patients, caregivers, appointments, and medical data in a modern, responsive interface.

## 🚀 Features

- **Patient Management**: Complete patient profiles, medical history, and care tracking
- **Caregiver Management**: Staff management with role-based access control
- **Appointment Scheduling**: Advanced calendar system with appointment management
- **Dashboard Analytics**: Multiple dashboard variants with real-time data visualization
- **Messaging System**: Integrated communication between doctors and patients
- **Financial Management**: Billing and financial tracking capabilities
- **Task Management**: Care task assignment and tracking
- **Medical Tests**: Test results management and tracking
- **Reviews & Ratings**: Patient feedback and rating system
- **Responsive Design**: Mobile-first approach with cross-device compatibility
- **Dark/Light Mode**: Theme switching with contrast mode support
- **RTL Support**: Right-to-left language support
- **Real-time Notifications**: Push notifications and alerts

## 🛠️ Tech Stack

- **Frontend**: React 18.2.0
- **State Management**: Redux Toolkit
- **Routing**: React Router v6
- **UI Framework**: Material-UI (MUI) v7
- **Styling**: Styled Components + Emotion
- **Charts**: Recharts + Chart.js
- **Animations**: Framer Motion
- **Forms**: React Hook Form with Zod validation
- **Date Handling**: Moment.js
- **Build Tool**: Create React App with CRACO
- **Testing**: Jest + React Testing Library

## 📦 Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd insyt-care-dashboard
   ```

2. **Install dependencies**

   ```bash
   npm install
   # or
   yarn install
   ```

3. **Start the development server**

   ```bash
   npm start
   # or
   yarn start
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🔧 Available Scripts

### `npm start` / `yarn start`

Runs the app in development mode using CRACO.

- Opens [http://localhost:3000](http://localhost:3000) in your browser
- Hot reloading enabled
- Lint errors displayed in console

### `npm run build` / `yarn build`

Builds the app for production to the `build` folder.

- Optimized React production build
- Minified files with hashed filenames
- Ready for deployment

### `npm run eject`

**⚠️ One-way operation - cannot be undone!**
Ejects from Create React App to expose configuration files.

### `npm run vercel:prod` / `npm run vercel:test`

Deployment scripts for Vercel platform.

## 📁 Project Structure

```
insyt-care-dashboard/
├── public/                     # Static files
│   ├── favicon/               # Favicon files
│   ├── index.html            # Main HTML template
│   └── firebase-messaging-sw.js # Service worker for notifications
├── src/
│   ├── assets/               # Media files (images, icons)
│   ├── components/           # Reusable UI components
│   ├── constants/            # Application constants
│   ├── contexts/             # React context providers
│   ├── db/                   # Mock data and database utilities
│   ├── fonts/                # Custom fonts (including icomoon icons)
│   ├── hooks/                # Custom React hooks
│   ├── layout/               # Layout components (sidebar, header, etc.)
│   ├── pages/                # Page components
│   │   ├── Dashboard.jsx     # Main dashboard
│   │   ├── Patients.jsx      # Patient management
│   │   ├── Caregivers.jsx    # Caregiver management
│   │   ├── Appointments.jsx  # Appointment scheduling
│   │   └── ...               # Other page components
│   ├── store/                # Redux store configuration
│   ├── styles/               # Global styles and themes
│   ├── UI/                   # Basic UI components (buttons, inputs)
│   ├── utils/                # Utility functions
│   ├── widgets/              # Dashboard widgets
│   ├── App.jsx               # Main application component
│   ├── AppLayout.jsx         # Application layout wrapper
│   └── index.jsx             # Application entry point
├── babel-plugin-macros.config.js # Babel macros configuration
├── craco.config.js           # CRACO configuration overrides
├── jsconfig.json             # JavaScript configuration
└── package.json              # Dependencies and scripts
```

## 🎨 Key Pages & Features

### Dashboard Variants

- **Dashboard A-K**: Multiple dashboard layouts for different user roles
- **Analytics**: Real-time data visualization and metrics
- **Customizable Widgets**: Drag-and-drop dashboard customization

### Patient Management

- **Patient Profiles**: Comprehensive patient information
- **Medical History**: Complete medical records tracking
- **Appointment History**: Patient appointment timeline
- **Test Results**: Medical test management and results

### Staff Management

- **Caregivers**: Healthcare provider management
- **Nurses**: Nursing staff coordination
- **Staff**: General staff administration
- **Role-based Access**: Permission management system

### Communication

- **Messenger**: Real-time messaging between staff and patients
- **Notifications**: System-wide notification management
- **Reviews**: Patient feedback and rating system

## 📚 Key Dependencies

### Core Framework

- **[React](https://reactjs.org/)** `^18.2.0` - Frontend framework
- **[Redux Toolkit](https://redux-toolkit.js.org/)** `^1.8.3` - State management
- **[React Router](https://reactrouter.com/)** `^6.4.1` - Client-side routing
- **[CRACO](https://github.com/dilanx/craco)** `^6.4.5` - Create React App configuration override

### UI & Styling

- **[Material-UI](https://mui.com/)** `^7.0.2` - React UI framework
- **[Styled Components](https://styled-components.com/)** `^5.3.5` - CSS-in-JS styling
- **[Emotion](https://emotion.sh/)** `^11.14.0` - CSS-in-JS library
- **[Polished](https://polished.js.org/)** `^4.2.2` - CSS helper functions
- **[Framer Motion](https://www.framer.com/motion/)** `^6.4.3` - Animation library

### Data Visualization

- **[Recharts](http://recharts.org/)** `^2.1.12` - React charts library
- **[Chart.js](https://www.chartjs.org/)** `^3.9.1` - Canvas-based charts
- **[React D3 Speedometer](https://palerdot.in/react-d3-speedometer/)** `^1.0.2` - Gauge charts
- **[React Cardiogram](https://github.com/dmitriy-kudelko/react-cardiogram)** `^1.0.0-beta.7` - Heart rate visualization

### Forms & Validation

- **[React Hook Form](https://react-hook-form.com/)** `^7.56.1` - Form management
- **[Zod](https://zod.dev/)** `^3.24.3` - Schema validation
- **[React Select](https://react-select.com/)** `^5.4.0` - Enhanced select components
- **[React Datepicker](https://reactdatepicker.com/)** `^4.8.0` - Date selection

### Layout & Interaction

- **[React Grid Layout](https://github.com/react-grid-layout/react-grid-layout)** `^1.3.4` - Draggable grid
- **[DND Kit](https://dndkit.com/)** `^6.0.5` - Drag and drop functionality
- **[React Dropzone](https://react-dropzone.js.org/)** `^14.2.2` - File upload
- **[Swiper](https://swiperjs.com/)** `^8.3.0` - Touch slider

### Calendar & Scheduling

- **[React Big Calendar](https://jquense.github.io/react-big-calendar/)** `^1.3.3` - Calendar component
- **[React Calendar](https://projects.wojtekmaj.pl/react-calendar/)** `^3.7.0` - Date picker calendar
- **[Moment.js](https://momentjs.com/)** `^2.29.4` - Date manipulation

### Notifications & Communication

- **[Notistack](https://iamhosseindhv.com/notistack)** `^2.0.5` - Notification system
- **[Firebase](https://firebase.google.com/)** `^11.6.0` - Backend services & push notifications

### Utilities

- **[Axios](https://axios-http.com/)** `^1.9.0` - HTTP client
- **[Lodash](https://lodash.com/)** `^4.17.21` - Utility functions
- **[UUID](https://github.com/uuidjs/uuid)** `^11.1.0` - Unique ID generation
- **[Country State City](https://github.com/harpreetkhalsagtbit/country-state-city)** `^3.0.1` - Location data

## 🔧 Configuration

### Environment Setup

Create a `.env` file in the root directory:

```env
REACT_APP_API_URL=your_api_url
REACT_APP_FIREBASE_API_KEY=your_firebase_key
REACT_APP_FIREBASE_AUTH_DOMAIN=your_auth_domain
REACT_APP_FIREBASE_PROJECT_ID=your_project_id
```

### CRACO Configuration

The project uses CRACO for customizing Create React App without ejecting. Key configurations include:

- Path aliases for cleaner imports (`@components`, `@pages`, `@utils`, etc.)
- Webpack optimizations for better performance
- Babel plugin configurations for styled-components

### Icon Customization

To customize the icon font:

1. Visit [IcoMoon App](https://icomoon.io/app)
2. Import the `selection.json` file from `src/fonts/icomoon/`
3. Add/modify icons as needed
4. Download and replace the font files

## 🚀 Deployment

### Vercel (Recommended)

```bash
npm run vercel:prod  # Production deployment
npm run vercel:test  # Test deployment
```

### Manual Build

```bash
npm run build
# Deploy the 'build' folder to your hosting provider
```

### Build Optimizations

The project includes several build optimizations:

- Image compression with WebP support
- CSS minification and optimization
- Bundle splitting for better caching
- Service worker for offline functionality

## 🧪 Testing

Run the test suite:

```bash
npm test
# or
yarn test
```

The project uses Jest and React Testing Library for unit and integration testing.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow the existing code style and structure
- Write tests for new features
- Update documentation as needed
- Ensure all tests pass before submitting PR

## 📄 License

This project is licensed under the Envato License. See the [license](https://1.envato.market/KYbje) for details.

**Copyright**: Merkulove ( https://merkulov.design/ ). All rights reserved.

## 👥 Credits

- **Author**: Merkulove Design
- **Contributors**: Lilit L. (<EMAIL>)
- **Support**: <EMAIL>
- **Website**: [https://merkulov.design/](https://merkulov.design/)

## 🆘 Support

For support and questions:

- **Email**: <EMAIL>
- **Documentation**: [Create React App Docs](https://create-react-app.dev/docs/getting-started/)
- **Material-UI**: [MUI Documentation](https://mui.com/getting-started/installation/)
- **Styled Components**: [Documentation](https://styled-components.com/docs)

## 🔄 Version History

- **v1.0.2** - Current version with enhanced features and optimizations
- **v1.0.0** - Initial release

---

Built with ❤️ by [Merkulove Design](https://merkulov.design/)
