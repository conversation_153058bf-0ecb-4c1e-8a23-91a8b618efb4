importScripts("https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js");
importScripts("https://www.gstatic.com/firebasejs/8.10.0/firebase-messaging.js");

const firebaseConfig = {
  apiKey: "AIzaSyCF3RwKjw3n55nzD2p2aOY1ScHBLYlbRUQ",
  authDomain: "insyt-care.firebaseapp.com",
  projectId: "insyt-care",
  storageBucket: "insyt-care.firebasestorage.app",
  messagingSenderId: "92475714813",
  appId: "1:92475714813:web:b9314de8ae145364ace294",
  measurementId: "G-KFE07JXQ32",
};

firebase.initializeApp(firebaseConfig);
const messaging = firebase.messaging();

messaging.onBackgroundMessage((payload) => {
  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: "./favicon.ico",
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});

self.addEventListener("notificationclick", (event) => {
  event.notification.close();
  
  // Handle notification click - could open the app or a specific page
  event.waitUntil(
    clients.openWindow("/")
  );
});
