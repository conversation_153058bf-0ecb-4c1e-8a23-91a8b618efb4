import * as React from "react";
import Box from "@mui/material/Box";
import CircularProgress, { circularProgressClasses } from "@mui/material/CircularProgress";
import { Typography } from "@mui/material";
// import {theme} from "../../styles/mui-theme";

// Inspired by the former Facebook spinners.
export default function CircularProgressWithLabel({ value, size, fontSize }) {
  return (
    <Box sx={{ position: "relative", height: size || 40, width: size || 40 }}>
      <CircularProgress
        variant="determinate"
        sx={(theme) => ({
          color: theme.palette.grey[200],
          ...theme.applyStyles("dark", {
            color: theme.palette.grey[800],
          }),
        })}
        size={size || 40}
        thickness={4}
        value={100}
      />
      <CircularProgress
        variant="determinate"
        disableShrink
        sx={(theme) => ({
          color: theme.palette.primary.main,
          animationDuration: "550ms",
          position: "absolute",
          left: 0,
          [`& .${circularProgressClasses.circle}`]: {
            strokeLinecap: "round",
          },
          ...theme.applyStyles("dark", {
            color: theme.palette.primary.dark,
          }),
        })}
        size={size || 40}
        thickness={4}
        value={value}
      />

      <Box
        sx={{
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          position: "absolute",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Typography variant="caption" component="div" sx={{ color: "text.secondary" }} fontSize={fontSize || "12px"}>
          {`${value || 0}%`}
        </Typography>
      </Box>
    </Box>
  );
}
