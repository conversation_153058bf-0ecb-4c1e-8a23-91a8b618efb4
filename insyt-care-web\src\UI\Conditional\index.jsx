import React from "react";

const Conditional = ({ value, children }) => {
  const ifChild = React.Children.toArray(children).find(
    (child) => React.isValidElement(child) && child.type === Conditional.If,
  );

  const elseChild = React.Children.toArray(children).find(
    (child) => React.isValidElement(child) && child.type === Conditional.Else,
  );

  return <>{value ? ifChild : elseChild || null}</>;
};

const ConditionalIf = ({ children }) => <>{children}</>;
ConditionalIf.displayName = "Conditional.If";
Conditional.If = ConditionalIf;

const ConditionalElse = ({ children }) => <>{children}</>;
ConditionalElse.displayName = "Conditional.Else";
Conditional.Else = ConditionalElse;

export { Conditional };
