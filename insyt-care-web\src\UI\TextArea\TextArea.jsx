// styling
import styled from "styled-components/macro";
import { colors, dark, light, textSizes } from "@styles/vars";
import theme from "styled-theming";

// utils
import PropTypes from "prop-types";

export const StyledTextArea = styled.textarea`
  height: auto;
  padding: 10px 16px;
  border-radius: 8px;
  border: 1px solid transparent;
  font-size: ${textSizes["14"]};
  resize: vertical;
  width: 100%;
  ${theme("theme", {
    light: `
    background-color: ${light.highlight};
   `,
    dark: `
    background-color: ${dark.highlight};
   `,
  })};
  transition: border-color var(--transition), box-shadow var(--transition);

  &.error {
    border-color: ${colors.error};
  }

  &:hover {
    box-shadow: ${theme("theme", {
      light: `0 0 0 2px ${colors.blue}`,
      dark: `0 0 0 2px ${colors.blue}`,
    })};
  }

  &:focus {
    box-shadow: 0 0 0 2px ${colors.blue};
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    box-shadow: none;
    background-color: ${theme("theme", {
      light: `${light.highlight}`,
      dark: `${dark.highlight}`,
    })};
  }
`;

const TextArea = ({ placeholder, value, handler, id, className, rows, ...rest }) => {
  return (
    <StyledTextArea
      placeholder={placeholder}
      value={value}
      onChange={handler}
      id={id}
      className={className || ""}
      rows={rows}
      {...rest}
    />
  );
};

TextArea.propTypes = {
  placeholder: PropTypes.string,
  value: PropTypes.string,
  handler: PropTypes.func.isRequired,
  id: PropTypes.string.isRequired,
  className: PropTypes.string,
  rows: PropTypes.number,
};

TextArea.defaultProps = {
  rows: 3,
};

export default TextArea;
