// styling
import styled from "styled-components/macro";
import { colors, flex, textSizes } from "@styles/vars";

// utils
import PropTypes from "prop-types";
import { colorTypes } from "@constants/colors";

const Wrapper = styled.div`
  display: flex;
  ${flex.center};
  border-radius: 20px;
  padding: 10px 16px;
  color: #fff;
  font-size: ${textSizes["14"]};
  gap: 10px;
  background-color: ${(props) => colors[colorTypes.find((item) => item.cat === props.reminder).color]};

  span {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
`;

const AppointmentChip = ({ label, icon }) => {
  return (
    <Wrapper className="reminder" reminder={"checkup"}>
      {icon && <i className={`icon icon-${icon}`}></i>}
      <span>{label}</span>
    </Wrapper>
  );
};

export default AppointmentChip;
