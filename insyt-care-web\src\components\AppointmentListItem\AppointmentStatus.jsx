// styling
import styled from "styled-components/macro";
import { flex, textSizes } from "@styles/vars";

const statusColors = {
  SCHEDULED: "#FFA500", // Orange
  COMPLETED: "#4CAF50", // Green
  CANCELLED: "#F44336", // Red
  MISSED: "#FFEB3B",
  LATE: "#FF6B3520", // Orange-Red for late appointments
  "NOT SUBMITTED": "#FF6B3520", 
  SUBMITTED:'#2563EB50',
  "SIGNATURE PENDING":"#2563EB26"
};

const Wrapper = styled.div`
  display: flex;
  ${flex.center};
  border-radius: 20px;
  padding: 10px 16px;
  color: ${(props) => props.status === "LATE" ? "#F44336" : "#fff"};
  font-size: ${textSizes["14"]};
  gap: 10px;
  background-color: ${(props) => statusColors[props.status] || "#109193"}; 

  span {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-transform: capitalize;
  }
`;

const AppointmentStatus = ({ icon, status }) => {
  // Map status for display
  let displayStatus = status;
  if (status === "SUBMITTED") {
    displayStatus = "SIGNATURE PENDING";
  } else if (status === "LATE") {
    displayStatus = "NOT SUBMITTED";
  }
  
  return (
    <Wrapper className="reminder" status={status}>
      {icon}
      <span>{displayStatus?.toLowerCase()}</span>
    </Wrapper>
  );
};

export default AppointmentStatus;
