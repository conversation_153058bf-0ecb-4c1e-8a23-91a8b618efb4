import { colors, dark, flex, light, textSizes, breakpoints } from "@styles/vars";
import styled from "styled-components/macro";
import { colorTypes } from "@constants/colors";
import theme from "styled-theming";
import { ModalContent } from "@components/ModalWindow";

const disabled = (theme) => (theme === "dark" ? light.text : "#DCE2E8");

const available = (theme) => (theme === "dark" ? dark.bodyBg : light.bodyBg);

export const StyledEvent = styled.div`
  background-color: ${(props) =>
    props.type !== "disabled" &&
    props.type !== "available" &&
    colors[colorTypes.find((color) => color?.cat === props?.type)?.color]};
  transition: all var(--transition);
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 5;
  }

  .cover {
    display: flex;
    ${flex.center};
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: ${colors.blue};
    color: #fff;
    z-index: 30;
    visibility: hidden;
    opacity: 0;
    transition: opacity var(--transition), visibility var(--transition);
    font-size: ${textSizes["16"]};
  }

  ${(props) =>
    props.type === "disabled" &&
    `
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
    transform: scaleX(-1);
    background-size: 10px 10px;
    background-image: repeating-linear-gradient(45deg, ${disabled(props.mode)} 0,
     ${disabled(props.mode)} 1px, transparent 0, transparent 50%);
     pointer-events: none;
  `};

  ${(props) =>
    props.type === "available" &&
    `
    width: 100% !important;
    height: 100% !important;
    border-radius: 0 !important;
    background-color: ${available(props.mode)};
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  `};

  &:after {
    content: "";
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    width: 100%;
    height: calc(100% - 1px);
    mix-blend-mode: luminosity;
    background-color: ${theme("theme", {
      light: light.bodyBg,
      dark: dark.bodyBg,
    })};
    opacity: ${theme("theme", {
      light: 1,
      dark: 0.8,
    })};
    display: ${(props) => (props.type !== "disabled" && props.type !== "available" ? "block" : "none")};
  }

  &.isEnded {
    background-color: ${(props) => props.type !== "disabled" && props.type !== "available" && colors.gray};
    opacity: ${(props) => props.type !== "disabled" && props.type !== "available" && 0.5};
  }

  .icon {
    color: ${colors.blue};
  }

  .event-title {
    font-size: ${textSizes["12"]};
    position: relative;
    z-index: 2;
    color: ${theme("theme", {
      light: light.text,
      dark: "#fff",
    })};
    line-height: 1.3;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    // padding-right: 24px; /* Make space for the count badge */
  }
  .event-status {
    font-size: ${textSizes["7"]};
    position: relative;
    z-index: 2;
    display: flex;
    line-height: 1.3;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    justify-content: center;
    white-space: nowrap;
    text-transform: capitalize;

    /* Responsive font sizes for small screens */
    @media (max-width: 480px) {
      font-size: ${textSizes["7"]};
    }

    @media (max-width: 320px) {
      font-size: ${textSizes["7"]};
    }
  }

  .appointment-count {
    position: absolute;
    top: 4px;
    right: 4px;
    font-size: ${textSizes["9"]};
    font-weight: 600;
    color: #fff;
    // background-color: ${colors.blue};
    padding: 2px 5px;
    border-radius: 8px;
    line-height: 1;
    z-index: 3;
    min-width: 16px;
    text-align: center;
    // box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: ${colors.blue};
      transform: scale(1.1);
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    }
  }

  .event-time {
    font-size: ${textSizes["10"]};
    opacity: 0.8;
    margin-top: 2px;
    position: relative;
    z-index: 2;
    color: ${theme("theme", {
      light: light.text,
      dark: "#fff",
    })};
  }

  &.event-day {
    padding: 12px 16px;
    font-size: ${textSizes["12"]};
    min-height: 50px;
    height: 100%;
    ${flex.col};
    justify-content: flex-start;
    align-items: flex-start;
    text-align: left;
    border-left: 4px solid
      ${(props) =>
        props.type !== "disabled" &&
        props.type !== "available" &&
        colors[colorTypes?.find((color) => color?.cat === props?.type)?.color]};

    .event-title {
      font-size: ${textSizes["12"]};
      font-weight: 600;
      margin-bottom: 4px;
      white-space: normal;
      overflow: visible;
      text-overflow: unset;
      padding-right: 30px; /* More space for larger count badge in day view */
    }
    .event-status {
      font-size: ${textSizes["7"]};
      font-weight: 600;
      margin-bottom: 4px;
      white-space: normal;
      overflow: visible;
      text-overflow: unset;

      @media (max-width: 480px) {
        font-size: ${textSizes["7"]};
      }

      @media (max-width: 320px) {
        font-size: ${textSizes["7"]};
      }

      /* Large screens font size */
      @media (min-width: 1280px) {
        font-size: ${textSizes["12"]};
      }
    }

    .event-time {
      font-size: ${textSizes["12"]};
      opacity: 0.7;
    }

    .appointment-count {
      top: 8px;
      right: 8px;
      font-size: ${textSizes["10"]};
      padding: 3px 6px;
      min-width: 18px;
      cursor: pointer;
    }
  }

  &.event-week,
  &.event-month {
    min-width: 100%;
    min-height: 28px;
    // padding: 8px 12px;
    border-radius: 4px;
    ${flex.col};
    justify-content: center;
    align-items: flex-start;

    &:after {
      display: ${(props) => (props.type !== "disabled" && props.type !== "available" ? "block" : "none")};
    }

    .event-title {
      font-size: ${textSizes["12"]};
      display: block;
      line-height: 1.3;
      max-width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-weight: 500;
      padding-right: 20px; /* Space for count badge */
    }
    .event-status {
      font-size: ${textSizes["7"]};
      display: block;
      line-height: 1.3;
      max-width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-weight: 600;

      @media (max-width: 768px) {
        font-size: ${textSizes["7"]};
      }

      @media (max-width: 480px) {
        font-size: ${textSizes["7"]};
      }

      @media (max-width: 320px) {
        font-size: ${textSizes["7"]};
      }
    }

    .event-time {
      display: block;
      font-size: ${textSizes["10"]};
      margin-top: 2px;
      opacity: 0.8;
    }

    .appointment-count {
      top: 6px;
      right: 6px;
      font-size: ${textSizes["8"]};
      padding: 1px 4px;
      min-width: 14px;
      cursor: pointer;
    }
  }

  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: inline-block;
  }

  ${breakpoints.tablet} {
    &.event-week,
    &.event-month {
      min-width: 100%;
      min-height: 30px;
      // padding: 10px 14px;

      .event-title {
        font-size: ${textSizes["13"]};
        font-weight: 500;
        padding-right: 22px; /* Space for count badge */
      }
      .event-status {
        font-size: ${textSizes["7"]};
        font-weight: 600;
      }

      .event-time {
        font-size: ${textSizes["11"]};
      }

      .appointment-count {
        top: 8px;
        right: 8px;
        font-size: ${textSizes["8"]};
        padding: 2px 4px;
        min-width: 15px;
        cursor: pointer;
      }
    }
  }

  ${breakpoints.laptop} {
    &.event-week,
    &.event-month {
      min-width: 100%;
      min-height: 32px;
      // padding: 10px 14px;
      ${flex.col};
      justify-content: center;
      align-items: flex-start;

      &:after {
        display: ${(props) => (props.type !== "disabled" && props.type !== "available" ? "block" : "none")};
      }

      .event-title {
        font-size: ${textSizes["13"]};
        display: block;
        max-width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 500;
      }
      .event-status {
        font-size: ${textSizes["7"]};
        display: block;
        max-width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 600;
      }

      .event-time {
        display: block;
        font-size: ${textSizes["11"]};
        margin-top: 2px;
      }
    }

    &.event-month {
      min-height: 36px;

      ${(props) =>
        props.type === "available" &&
        `
        padding: 10px 14px;
        min-height: 40px;
      `};

      .event-title {
        font-size: ${textSizes["13"]};
        font-weight: 600;
      }
    }
  }

  ${breakpoints.laptopL} {
    &.event-week,
    &.event-month {
      min-height: 39px;
      // padding: 12px 16px;
      padding-left: 12px;

      .event-title {
        font-size: ${textSizes["12"]};
        font-weight: 500;
      }

      .event-status {
        font-size: ${textSizes["12"]};
        font-weight: 600;
      }

      .event-time {
        font-size: ${textSizes["12"]};
      }

      .appointment-count {
        top: 10px;
        right: 10px;
        font-size: ${textSizes["9"]};
        padding: 2px 5px;
        min-width: 16px;
        cursor: pointer;
      }
    }

    &.event-month {
      min-height: 40px;

      .event-title {
        font-size: ${textSizes["12"]};
        font-weight: 600;
        padding-right: 26px; /* Space for count badge */
      }

      .event-status {
        font-size: ${textSizes["12"]};
        font-weight: 600;
      }
    }
  }
`;

export const EventModal = styled(ModalContent)`
  ${flex.col};
  gap: 20px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 18px 20px 20px 20px; /* Increased top padding for close button */
  border-radius: 12px;
  background-color: ${theme("theme", {
    light: light.widgetBg,
    dark: dark.widgetBg,
  })};
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  width: 90vw;
  max-width: 400px;
  max-height: 80vh;
  overflow: visible; /* Changed from overflow-y: auto to allow close button to be visible */
  z-index: 1000;

  /* Ensure the modal content is scrollable when needed while keeping close button visible */
  .modal-content-wrapper {
    overflow-y: auto;
    max-height: calc(80vh - 70px); /* Account for padding and close button */
  }
  .block {
    ${flex.col};
    gap: 6px;

    &:first-of-type {
      .value {
        font-size: ${textSizes["14"]};
        font-weight: 600;
        color: ${theme("theme", {
          light: light.text,
          dark: dark.text,
        })};
      }
    }
    .label {
      font-size: ${textSizes["12"]};
      font-weight: 500;
      color: ${colors.gray};
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .value {
      font-size: ${textSizes["12"]};
      color: ${theme("theme", {
        light: light.text,
        dark: dark.text,
      })};
      line-height: 1.4;
    }
  } /* Mobile Small (320px+) */
  ${breakpoints.mobileS} {
    width: 95vw;
    max-width: 350px;
    padding: 18px 16px 16px 16px; /* Increased top padding */
    max-height: 75vh;

    .block {
      gap: 4px;

      &:first-of-type {
        .value {
          font-size: ${textSizes["14"]};
        }
      }

      .label {
        font-size: ${textSizes["11"]};
      }

      .value {
        font-size: ${textSizes["11"]};
      }
    }
  } /* Mobile Medium (375px+) */
  ${breakpoints.mobileM} {
    width: 90vw;
    max-width: 380px;
    padding: 18px 18px 18px 18px; /* Increased top padding */

    .block {
      &:first-of-type {
        .value {
          font-size: ${textSizes["15"]};
        }
      }

      .label {
        font-size: ${textSizes["12"]};
      }

      .value {
        font-size: ${textSizes["12"]};
      }
    }
  } /* Mobile Large (414px+) */
  ${breakpoints.mobileL} {
    width: 85vw;
    max-width: 420px;
    padding: 18px 20px 20px 20px; /* Increased top padding */

    .block {
      gap: 6px;

      &:first-of-type {
        .value {
          font-size: ${textSizes["16"]};
        }
      }

      .label {
        font-size: ${textSizes["12"]};
      }

      .value {
        font-size: ${textSizes["13"]};
      }
    }
  } /* Tablet (768px+) */
  ${breakpoints.tablet} {
    width: 80vw;
    max-width: 600px;
    max-height: 85vh;
    padding: 18px 28px 28px 28px; /* Increased top padding */

    .block {
      gap: 8px;

      &:first-of-type {
        .value {
          font-size: ${textSizes["18"]};
        }
      }

      .label {
        font-size: ${textSizes["13"]};
      }

      .value {
        font-size: ${textSizes["14"]};
      }
    }
  } /* Laptop (1024px+) */
  ${breakpoints.laptop} {
    width: 70vw;
    max-width: 700px;
    max-height: 90vh;
    padding: 20px 32px 32px 32px; /* Increased top padding */

    .block {
      gap: 10px;

      &:first-of-type {
        .value {
          font-size: ${textSizes["19"]};
        }
      }

      .label {
        font-size: ${textSizes["14"]};
      }

      .value {
        font-size: ${textSizes["15"]};
      }
    }
  } /* Large Laptop (1280px+) */
  ${breakpoints.laptopL} {
    width: 60vw;
    max-width: 800px;
    max-height: 90vh;
    padding: 25px 40px 40px 40px; /* Increased top padding */

    .block {
      gap: 12px;

      &:first-of-type {
        .value {
          font-size: ${textSizes["20"]};
        }
      }

      .label {
        font-size: ${textSizes["14"]};
      }

      .value {
        font-size: ${textSizes["16"]};
      }
    }
  } /* Desktop (1600px+) */
  ${breakpoints.desktop} {
    width: 50vw;
    max-width: 900px;
    max-height: 85vh;
    padding: 70px 44px 44px 44px; /* Increased top padding */

    .block {
      gap: 14px;

      &:first-of-type {
        .value {
          font-size: ${textSizes["22"]};
        }
      }

      .label {
        font-size: ${textSizes["15"]};
      }

      .value {
        font-size: ${textSizes["17"]};
      }
    }
  }
  .appointments-list {
    ${flex.col};
    gap: 8px;
    margin-top: 8px;
    max-height: 200px;
    overflow-y: auto;

    .appointment-item {
      display: flex;
      justify-content: space-between;
      gap: 8px;
      padding: 8px;
      border-radius: 6px;
      background-color: ${theme("theme", {
        light: "rgba(0, 0, 0, 0.02)",
        dark: "rgba(255, 255, 255, 0.05)",
      })};
      border: 1px solid
        ${theme("theme", {
          light: "rgba(0, 0, 0, 0.08)",
          dark: "rgba(255, 255, 255, 0.1)",
        })};
      align-items: center;
      transition: all 0.2s ease;

      &.clickable {
        cursor: pointer;

        &:hover {
          background-color: ${theme("theme", {
            light: "rgba(59, 130, 246, 0.08)",
            dark: "rgba(59, 130, 246, 0.15)",
          })};
          border-color: ${colors.blue + "40"};
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }

      .appointment-time {
        font-size: ${textSizes["8"]};
        font-weight: 600;
        color: ${colors.blue};
        white-space: nowrap;
      }

      .appointment-details {
        ${flex.col};
        gap: 2px;
        min-width: 0;
        flex: 1;

        .appointment-name {
          font-size: ${textSizes["11"]};
          font-weight: 500;
          color: ${theme("theme", {
            light: light.text,
            dark: dark.text,
          })};
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .appointment-type {
          font-size: ${textSizes["9"]};
          color: ${colors.gray};
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .appointment-status {
        font-size: ${textSizes["7"]};
        font-weight: 500;
        width: fit-content;
        padding: 2px 4px;
        border-radius: 8px;
        white-space: nowrap;
        text-transform: capitalize;
        background-color: rgb(223, 233, 230);
      }
    }
  }

  /* Mobile Small responsive adjustments */
  ${breakpoints.mobileS} {
    .appointments-list {
      gap: 6px;
      max-height: 150px;

      .appointment-item {
        gap: 6px;
        padding: 6px;

        .appointment-time {
          font-size: ${textSizes["7"]};
        }

        .appointment-details {
          .appointment-name {
            font-size: ${textSizes["10"]};
          }

          .appointment-type {
            font-size: ${textSizes["8"]};
          }
        }

        .appointment-status {
          font-size: ${textSizes["6"]};
          padding: 1px 3px;
        }
      }
    }
  }

  /* Mobile Medium responsive adjustments */
  ${breakpoints.mobileM} {
    .appointments-list {
      gap: 8px;
      max-height: 180px;

      .appointment-item {
        gap: 8px;
        padding: 8px;

        .appointment-time {
          font-size: ${textSizes["8"]};
        }

        .appointment-details {
          .appointment-name {
            font-size: ${textSizes["11"]};
          }

          .appointment-type {
            font-size: ${textSizes["9"]};
          }
        }

        .appointment-status {
          font-size: ${textSizes["7"]};
          padding: 2px 4px;
        }
      }
    }
  }

  /* Mobile Large responsive adjustments */
  ${breakpoints.mobileL} {
    .appointments-list {
      gap: 10px;
      max-height: 220px;

      .appointment-item {
        gap: 10px;
        padding: 10px;

        .appointment-time {
          font-size: ${textSizes["9"]};
        }

        .appointment-details {
          .appointment-name {
            font-size: ${textSizes["12"]};
          }

          .appointment-type {
            font-size: ${textSizes["10"]};
          }
        }

        .appointment-status {
          font-size: ${textSizes["7"]};
          padding: 3px 5px;
        }
      }
    }
  }
  /* Tablet responsive adjustments */
  ${breakpoints.tablet} {
    .appointments-list {
      gap: 12px;
      max-height: 300px;

      .appointment-item {
        padding: 12px;
        gap: 12px;

        .appointment-time {
          font-size: ${textSizes["10"]};
        }

        .appointment-details {
          .appointment-name {
            font-size: ${textSizes["14"]};
          }

          .appointment-type {
            font-size: ${textSizes["12"]};
          }
        }

        .appointment-status {
          font-size: ${textSizes["8"]};
          padding: 4px 8px;
        }
      }
    }
  }

  /* Laptop responsive adjustments */
  ${breakpoints.laptop} {
    .appointments-list {
      gap: 14px;
      max-height: 350px;

      .appointment-item {
        padding: 14px;
        gap: 14px;

        .appointment-time {
          font-size: ${textSizes["12"]};
        }

        .appointment-details {
          .appointment-name {
            font-size: ${textSizes["15"]};
          }

          .appointment-type {
            font-size: ${textSizes["13"]};
          }
        }

        .appointment-status {
          font-size: ${textSizes["9"]};
          padding: 5px 10px;
        }
      }
    }
  }

  /* Large Laptop responsive adjustments */
  ${breakpoints.laptopL} {
    .appointments-list {
      gap: 16px;
      max-height: 400px;

      .appointment-item {
        padding: 16px;
        gap: 16px;

        .appointment-time {
          font-size: ${textSizes["13"]};
        }

        .appointment-details {
          .appointment-name {
            font-size: ${textSizes["16"]};
          }

          .appointment-type {
            font-size: ${textSizes["14"]};
          }
        }

        .appointment-status {
          font-size: ${textSizes["10"]};
          padding: 6px 12px;
        }
      }
    }
  }

  /* Desktop responsive adjustments */
  ${breakpoints.desktop} {
    .appointments-list {
      gap: 18px;
      max-height: 450px;

      .appointment-item {
        padding: 18px;
        gap: 18px;

        .appointment-time {
          font-size: ${textSizes["14"]};
        }

        .appointment-details {
          .appointment-name {
            font-size: ${textSizes["17"]};
          }

          .appointment-type {
            font-size: ${textSizes["15"]};
          }
        }

        .appointment-status {
          font-size: ${textSizes["11"]};
          padding: 7px 14px;
        }
      }
    }
  }
  .add-appointment-btn {
    margin-top: 0.5rem;
    padding: 8px 12px;
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    font-size: ${textSizes["12"]};
    text-align: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
    width: 100%;

    &:hover {
      background-color: #43a047;
    }
  }

  /* Mobile responsive adjustments for button */
  ${breakpoints.mobileS} {
    .add-appointment-btn {
      padding: 6px 10px;
      font-size: ${textSizes["11"]};
      border-radius: 4px;
    }
  }

  ${breakpoints.mobileM} {
    .add-appointment-btn {
      padding: 8px 12px;
      font-size: ${textSizes["12"]};
      border-radius: 6px;
    }
  }

  ${breakpoints.mobileL} {
    .add-appointment-btn {
      padding: 10px 14px;
      font-size: ${textSizes["13"]};
    }
  }

  /* Tablet and larger responsive adjustments for button */
  ${breakpoints.tablet} {
    .add-appointment-btn {
      padding: 12px 16px;
      font-size: ${textSizes["14"]};
      border-radius: 8px;
    }
  }

  ${breakpoints.laptop} {
    .add-appointment-btn {
      padding: 14px 18px;
      font-size: ${textSizes["15"]};
    }
  }

  ${breakpoints.laptopL} {
    .add-appointment-btn {
      padding: 16px 20px;
      font-size: ${textSizes["16"]};
    }
  }

  ${breakpoints.desktop} {
    .add-appointment-btn {
      padding: 18px 22px;
      font-size: ${textSizes["17"]};
    }
  }
`;
