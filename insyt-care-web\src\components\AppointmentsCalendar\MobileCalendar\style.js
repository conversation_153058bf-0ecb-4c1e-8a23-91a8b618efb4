import styled from "styled-components/macro";
import { flex, colors, textSizes, dark, light, breakpoints } from "@styles/vars";
import theme from "styled-theming";

const borderColor = theme("theme", {
  light: "#DCE2E8",
  dark: "#25292D",
});

const bgColor = theme("theme", {
  light: light.widgetBg,
  dark: dark.widgetBg,
});

const textColor = theme("theme", {
  light: light.text,
  dark: dark.text,
});

const highlightBg = theme("theme", {
  light: light.highlight,
  dark: dark.highlight,
});

export const Container = styled.div`
  background: ${bgColor};
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;

  ${props => props.viewType === "day" && `
    padding: 0;
    margin: 0;
    background: transparent;
  `}
`;

export const WeekDays = styled.div`
  display: grid;
  grid-template-columns: ${props => props.viewType === "day" ? "1fr" : "repeat(7, 1fr)"};
  gap: 4px;
  margin-bottom: 8px;

  .weekday {
    text-align: center;
    font-size: ${textSizes["12"]};
    font-weight: 600;
    color: ${colors.gray};
    padding: 8px 4px;
    text-transform: uppercase;
  }
`;

export const CalendarGrid = styled.div`
  display: grid;
  grid-template-columns: ${props => props.viewType === "day" ? "1fr" : "repeat(7, 1fr)"};
  gap: 4px;

  ${props => props.viewType === "day" && `
    gap: 0;
  `}
`;

export const DayCell = styled.div`
  position: relative;
  aspect-ratio: ${props => props.viewType === "day" ? "auto" : "1"};
  ${flex.center};
  flex-direction: column;
  border-radius: 6px;
  cursor: ${props => props.selectable ? 'pointer' : 'default'};
  transition: all 0.2s ease;
  
  }};

  opacity: ${props => {
    if (!props.isCurrentMonth && props.viewType !== "day") return 0.3;
    if (!props.selectable) return 0.5;
    return 1;
  }};

  &:hover {
    background-color: ${props => {
      if (!props.selectable) return 'transparent';
      if (props.isSelected) return colors.blue;
      return highlightBg;
    }};
  }

  .day-number {
    font-size: ${props => props.viewType === "day" ? textSizes["18"] : textSizes["14"]};
    font-weight: ${props => props.isToday ? '600' : '400'};
    color: ${props => {
      if (props.isSelected) return '#fff';
      if (props.isToday && props.viewType !== "day") return '#fff';
      if (props.isToday && props.viewType === "day") return colors.blue;
      return textColor;
    }};
    text-align: center;
    display: ${props => props.isToday && props.viewType !== "day" ? "flex" : "block"};
    align-items: ${props => props.isToday && props.viewType !== "day" ? "center" : "initial"};
    justify-content: ${props => props.isToday && props.viewType !== "day" ? "center" : "initial"};
    width: ${props => props.isToday && props.viewType !== "day" ? "24px" : "auto"};
    height: ${props => props.isToday && props.viewType !== "day" ? "24px" : "auto"};
    border-radius: ${props => props.isToday && props.viewType !== "day" ? "50%" : "0"};
    background-color: ${props => props.isToday && props.viewType !== "day" ? colors.blue : "transparent"};
    margin: ${props => props.viewType === "day" ? "0 0 16px 0" : (props.isToday && props.viewType !== "day" ? "0 auto" : "0")};
    line-height: ${props => props.isToday && props.viewType !== "day" ? "1" : "normal"};
  }

  .event-indicator {
    position: relative;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: blue;
    display: ${props => props.viewType === "day" ? "none" : "block"};
    margin: 2px auto 0 auto;
  }

  .appointments-list {
    width: 100%;
    text-align: left;

    .appointment-item {
      background: ${highlightBg};
      padding: 8px 12px;
      margin: 4px 0;
      border-radius: 4px;
      font-size: ${textSizes["12"]};
      color: ${textColor};
      border-left: 3px solid ${colors.blue};
    }
  }

  ${props => props.viewType === "day" && `
    padding: 16px;
    background: ${bgColor};
    border-radius: 8px;
    margin: 16px 0;
    align-items: flex-start;
    min-height: 200px;
  `}

  ${breakpoints.mobileS} {
    min-height: ${props => props.viewType === "day" ? "200px" : "40px"};

    .day-number {
      font-size: ${props => props.viewType === "day" ? textSizes["16"] : textSizes["13"]};
      width: ${props => props.isToday && props.viewType !== "day" ? "20px" : "auto"};
      height: ${props => props.isToday && props.viewType !== "day" ? "20px" : "auto"};
    }
  }

  ${breakpoints.mobileL} {
    min-height: ${props => props.viewType === "day" ? "200px" : "44px"};

    .day-number {
      font-size: ${props => props.viewType === "day" ? textSizes["18"] : textSizes["14"]};
      width: ${props => props.isToday && props.viewType !== "day" ? "24px" : "auto"};
      height: ${props => props.isToday && props.viewType !== "day" ? "24px" : "auto"};
    }
  }
`;
