import {
  Button,
  Dialog,
  DialogContent,
  DialogActions,
  DialogContentText,
  DialogTitle,
  Box,
  CircularProgress,
  Typography,
} from "@mui/material";
import HelpOutlineIcon from "@mui/icons-material/HelpOutline";
import Btn from "@ui/Btn";

export default function ConfirmationModal({
  isOpen,
  onClose,
  title,
  subtitle,
  confirmText = "Confirm",
  cancelText = "Cancel",
  onCancel,
  onConfirm,
  isLoading = false,
}) {
  return (
    <>
      <Dialog
        open={isOpen}
        onClose={onClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        sx={{ zIndex: 300000, minWidth: { xs: "90%", md: "500px" } }}
      >
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          flexDirection="column"
          sx={{ minWidth: { xs: "90%", md: "500px" }, minHeight: "300px" }}
        >
          <HelpOutlineIcon sx={{ height: 100, width: 100 }} />
          <DialogTitle id="alert-dialog-title" textAlign="center">
            {title}
          </DialogTitle>
          <Typography id="alert-dialog-description" width={"90%"} textAlign={"center"}>
            {subtitle}
          </Typography>

          <Box sx={{ width: "90%", mt: 3, display: "flex", columnGap: 2, height:"auto", marginBottom:"10px" }}>
            <Button
              onClick={onCancel}
              variant="contained"
              color="secondary"
              sx={{
                fontSize: "16px",
                py: 1.8,
                flexGrow: 1,
              }}
              disabled={isLoading}
            >
              {cancelText}
            </Button>
            <Button
              onClick={onConfirm}
              variant="contained" // Explicitly set the variant
              color="primary" // Explicitly set the color
              sx={{
                fontSize: "16px",
                py: 1.8,
                flexGrow: 1,
              }}
              disabled={isLoading}
            >
              {isLoading ? <CircularProgress size={16 } /> : confirmText}
            </Button>
          </Box>
        </Box>
      </Dialog>
    </>
  );
}
