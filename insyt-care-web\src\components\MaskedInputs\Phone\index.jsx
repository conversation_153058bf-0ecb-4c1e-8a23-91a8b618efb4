// components
import Field from "@ui/Field";
import { PatternFormat } from "react-number-format";

const Phone = ({ id, placeholder, onChange, value, ...props }) => {
  return (
    <Field
      as={PatternFormat}
      id={id}
      placeholder={placeholder || "+234 XX XXX XXXX"}
      format="+234 ## ### ####"
      mask="_"
      value={value}
      onValueChange={(values) => {
        onChange?.(values.value);
      }}
      {...props}
      style={{ width: "100%" }}
    />
  );
};

export default Phone;
