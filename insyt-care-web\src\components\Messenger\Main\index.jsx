// styled components
import { Container } from "@components/Messenger/style";

// components
import Header from "@components/Messenger/Header";
import Message from "@components/Messenger/Message";
import Input from "@components/Messenger/Input";
import GroupSeparator from "@ui/GroupSeparator";
import Tab from "react-bootstrap/Tab";
import ScrollContainer from "@components/ScrollContainer";

// utils
import moment from "moment";

// hooks
import { useRef, useEffect } from "react";
import { useSelector } from "react-redux";

// assets
import { collection, getDocs, onSnapshot, orderBy, query } from "firebase/firestore";
import { db } from "../../../config/firebase.config";
import { useState } from "react";
import { COLLECTIONS } from "@constants/app";

const Main = ({ user, variant, activeChat }) => {
  const { user: loggedin_user } = useSelector((state) => state.auth);
  const { chats: all_chats } = useSelector((state) => state.chats);
  const current_chat = all_chats?.find((item) => item?.id === activeChat);
  const [messages, setMessages] = useState([]);
  console.log("message >>", messages);

  const headerRef = useRef(null);
  const footerRef = useRef(null);

  const trackRef = useRef(null);

  useEffect(() => {
    if (trackRef.current) {
      trackRef.current.scrollTop = trackRef.current.scrollHeight;
    }
  }, [messages]);

  useEffect(() => {
    if (trackRef.current) {
      trackRef.current.scrollTo(0, 0);
    }
  }, [user]);

  useEffect(() => {
    const messagesRef = collection(db, COLLECTIONS.CHATS, activeChat, "Messages");
    const q = query(messagesRef, orderBy("createdAt", "asc"));
    const unsubscribe = onSnapshot(q, (snapshot) => {
      const messages = snapshot.docs.map((item) => ({ id: item.id, ...item.data() }));
      const formatted_messages = messages.map((item) => ({
        id: item.id,
        sender: item?.sender,
        recipient: item?.recipient,
        message: item?.text,
        date: moment(item?.createdAt?.toDate()).valueOf(),
        read: item?.read,
        media: item?.image?.url,
      }));
      setMessages([{ chatHistory: formatted_messages, ...current_chat }]);
    });

    return () => {
      unsubscribe();
    };
  }, [user]);

  return (
    <Container>
      {current_chat?.otherUser && <Header variant={variant} user={current_chat?.otherUser} elRef={headerRef} />}
      <ScrollContainer height={"230"}>
        <div className="track" ref={trackRef} style={{ padding: "20px 0" }}>
          {messages?.map((item) => {
            const uniqueDates = [
              ...new Set(item.chatHistory.map((message) => moment(message.date).format("DD.MM.YYYY"))),
            ];

            return (
              <Tab.Pane eventKey={item.id} key={item.id}>
                {uniqueDates.map((date) => {
                  return (
                    <div className="group" key={`${item.id}-${date}`}>
                      <GroupSeparator text={date === moment().format("DD.MM.YYYY") ? "Today" : date} />
                      {item.chatHistory
                        .filter((message) => moment(message.date).format("DD.MM.YYYY") === date)
                        .map((message, i) => {
                          return (
                            <Message
                              key={message.id}
                              data={message}
                              senderName={`${item?.otherUser?.firstName}`}
                              myId={loggedin_user?.id}
                            />
                          );
                        })}
                    </div>
                  );
                })}
                {/* {item.isTyping && (
                  <Animation>
                    <Lottie animationData={typing} />
                  </Animation>
                )} */}
              </Tab.Pane>
            );
          })}
        </div>
      </ScrollContainer>
      <Input
        db={variant}
        id={user}
        elRef={footerRef}
        activeChat={activeChat}
        currentChat={messages[0]}
        otherUserId={current_chat?.otherUser?.id}
      />
    </Container>
  );
};

export default Main;
