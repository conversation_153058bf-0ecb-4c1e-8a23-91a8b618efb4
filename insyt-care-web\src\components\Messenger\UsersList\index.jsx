// styled components
import Field from "@ui/Field";
import { <PERSON><PERSON>, Header, List } from "@components/Messenger/style";
import { Container, But<PERSON> } from "@ui/TabNav/style";

// components
import Widget from "@components/Widget";
import Nav from "react-bootstrap/Nav";
import User from "@components/Messenger/UsersList/User";
import ScrollContainer from "@components/ScrollContainer";
import NoDataPlaceholder from "@components/NoDataPlaceholder";

// utils
import PropTypes from "prop-types";

// hooks
import { useRef, useState } from "react";
import useContentHeight from "@hooks/useContentHeight";

// data placeholder
import { doctor, patient } from "@db/messenger";
import { useDispatch, useSelector } from "react-redux";
import { Box } from "@mui/material";
import WidgetsLoader from "@components/WidgetsLoader";
import { useEffect } from "react";
import { collection, onSnapshot, orderBy, query } from "firebase/firestore";
import { db } from "../../../config/firebase.config";
import { COLLECTIONS } from "@constants/app";
import { getAllChats, setChatsAction } from "@store/slices/chats";
import { generateNames } from "@utils/helpers";

const UserList = ({ variant, user, onUserSelect, setModal, activeList, setActiveList, activeChat }) => {
  const [searchText, setSearchText] = useState("");

  const headerRef = useRef(null);
  const footerRef = useRef(null);
  const height = useContentHeight(headerRef, footerRef);

  const { chats } = useSelector((state) => state.chats);
  const caregiver_chats = chats?.filter((item) => item?.otherUser?.role === "CAREGIVER");
  const client_chats = chats?.filter((item) => item?.otherUser?.role === "CLIENT");
  const { user: logged_in_user } = useSelector((state) => state.auth);
  const dispatch = useDispatch();

  const drawList = (arr, role) => {
    const list = arr
      .filter((item) => {
        const fullName = `${item.firstName} ${item.lastName}`;
        return fullName.toLowerCase().includes(searchText.toLowerCase()) && item.role === role;
      })
      .map((item) => (
        <Nav.Link as="div" key={item?.id} eventKey={item?.id} onClick={() => onUserSelect(item?.id)}>
          <User user={user} data={item} onUserSelect={onUserSelect} setModal={setModal} />
        </Nav.Link>
      ));
    return list.length ? list : <NoDataPlaceholder />;
  };

  const render_chats = (arr = []) => {
    const list = arr
      ?.filter((item) => {
        const fullName = `${item?.otherUser?.firstName} ${item?.otherUser?.lastName}`;
        return fullName.toLowerCase().includes(searchText.toLowerCase());
      })
      ?.map((item) => (
        <Nav.Link as="div" key={item.id} eventKey={item.id}>
          <User
            user={user}
            data={item?.otherUser}
            lastMessage={item?.lastMessage}
            onUserSelect={onUserSelect}
            setModal={setModal}
            activeChat={activeChat}
            chat={item}
            unread={item?.unreadCounts?.[logged_in_user?.id]}
          />
        </Nav.Link>
      ));
    return list.length ? list : <NoDataPlaceholder />;
  };

  useEffect(() => {
    const q = query(collection(db, COLLECTIONS.CHATS));
    const unsubscribe = onSnapshot(q, orderBy("updatedAt", "desc"), () => {
      if (logged_in_user?.id) {
        dispatch(getAllChats(logged_in_user?.id));
      }
    });

    return () => {
      unsubscribe();
    };
  }, [logged_in_user?.id]);

  return (
    <Widget name="MessengerUserList">
      {variant === "nurse" && (
        <>
          <Header ref={headerRef}>
            <Container>
              <Button
                className={activeList === "caregivers" ? "active" : ""}
                onClick={() => setActiveList("caregivers")}
              >
                Caregivers
              </Button>
              <Button className={activeList === "clients" ? "active" : ""} onClick={() => setActiveList("clients")}>
                Patients
              </Button>
            </Container>
          </Header>
        </>
      )}

      <Footer ref={footerRef}>
        <div className="search">
          <Field
            type="search"
            placeholder={"Search"}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
          />
          <button className={searchText !== "" ? "visible" : ""} onClick={() => setSearchText("")}>
            <i className="icon icon-close" />
          </button>
        </div>
      </Footer>
      {variant === "nurse" && (
        <>
          {/* <Header ref={headerRef}>
            <Container>
              <Button
                className={activeList === "caregivers" ? "active" : ""}
                onClick={() => setActiveList("caregivers")}
              >
                Caregivers
              </Button>
              <Button className={activeList === "clients" ? "active" : ""} onClick={() => setActiveList("clients")}>
                Patients
              </Button>
            </Container>
          </Header> */}
          <ScrollContainer height={height}>
            <List className="track">
              <div style={{ margin: "2px 0" }}>
                {activeList === "caregivers" ? render_chats(caregiver_chats) : null}
                {activeList === "clients" ? render_chats(client_chats) : null}
              </div>
            </List>
          </ScrollContainer>
        </>
      )}
      {variant === "no-role" && (
        <ScrollContainer height={height}>
          <List className="track">{render_chats([])}</List>
        </ScrollContainer>
      )}
    </Widget>
  );
};

UserList.propTypes = {
  variant: PropTypes.oneOf(["doctor", "patient"]).isRequired,
  onUserSelect: PropTypes.func.isRequired,
  user: PropTypes.any.isRequired,
};

export default UserList;
