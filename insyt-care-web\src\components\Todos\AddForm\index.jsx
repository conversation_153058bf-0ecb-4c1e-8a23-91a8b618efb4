// styled components
import { Form, BtnWrapper, InputWrapper, StyledField } from "./style";

// components
import CustomSelect from "@ui/Select";
import Field from "@ui/Field";
import Btn from "@ui/Btn";
import Grow from "@mui/material/Grow";
import Collapse from "@mui/material/Collapse";

// utils
import { useDispatch, useSelector } from "react-redux";
import { addTodo, toggleCollapse } from "@store/slices/todos";
import { tasksOptions } from "@constants/options";
import { useState } from "react";
import { nanoid } from "nanoid";
import { addDoc, collection, doc, setDoc, Timestamp } from "firebase/firestore";
import { db } from "@config/firebase.config";
import { COLLECTIONS } from "@constants/app";
import { addNewTaskAction, addNewTaskToList, createNewTaskDocThunk, setTaskDetailsAction } from "@store/slices/tasks";
import { Box, Button, IconButton } from "@mui/material";
import { Add } from "@mui/icons-material";
import { useSnackbar } from "notistack";

const AddForm = ({ isVisible, visibilityHandler, variant }) => {
  const { currentNurse, currentCaregiver, currentPatient, tasksDetails } = useSelector((state) => state.tasks);
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();

  const [text, setText] = useState("");
  const [isLoading, setLoading] = useState(false);

  const resetForm = () => {
    setText("");
    visibilityHandler(false);
    setLoading(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (text?.trim() !== "") {
      setLoading(true);
      const id = nanoid();
      const payload = {
        id,
        name: text,
        complete: false,
        timestamp: new Date().valueOf(),
        expanded: true,
        label: "work",
        createdAt: Timestamp.now(),
      };
      await setDoc(doc(db, COLLECTIONS.TASKS, id), payload)
        .then(() => {
          dispatch(addNewTaskAction(payload));
          enqueueSnackbar("New task added", { variant: "success" });
        })
        .catch(() => {
          enqueueSnackbar("Couldn't add new task", { variant: "error" });
        });
    }
    resetForm();
  };

  return (
    <Collapse in={isVisible}>
      <Grow in={isVisible} timeout={700}>
        <Form onSubmit={(e) => handleSubmit(e)} variant={variant}>
          <Box mb={1}>
            <StyledField
              type="text"
              id="allergies"
              value={text}
              placeholder="Type here"
              onChange={(e) => setText(e.target.value)}
              autoFocus
            />
          </Box>
          <BtnWrapper>
            <Btn text="Add" type="submit" className={isLoading ? "disabled" : "success"} />
            <Btn text="Cancel" className={isLoading ? "disabled" : "error"} handler={resetForm} />
          </BtnWrapper>
        </Form>
      </Grow>
    </Collapse>
  );
};

export default AddForm;
