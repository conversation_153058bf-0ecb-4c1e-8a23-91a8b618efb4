export const data = [
    {
        id: 'marvin_park',
        stats: [
            {sick: 154, cured: 97},
            {sick: 205, cured: 300},
            {sick: 369, cured: 207},
            {sick: 250, cured: 344},
            {sick: 200, cured: 100},
            {sick: 120, cured: 400},
            {sick: 80, cured: 150},
            {sick: 366, cured: 211},
            {sick: 600, cured: 400},
            {sick: 200, cured: 500},
            {sick: 160, cured: 200},
            {sick: 650, cured: 480}
        ]
    },
    {
        id: 'norman_munoz',
        stats: [
            {sick: 352, cured: 469},
            {sick: 488, cured: 610},
            {sick: 120, cured: 80},
            {sick: 250, cured: 344},
            {sick: 200, cured: 100},
            {sick: 120, cured: 400},
            {sick: 80, cured: 200},
            {sick: 740, cured: 400},
            {sick: 500, cured: 100},
            {sick: 200, cured: 156},
            {sick: 500, cured: 80},
            {sick: 100, cured: 20}
        ]
    },
    {
        id: 'tillie_mathis',
        stats: [
            {sick: 154, cured: 97},
            {sick: 205, cured: 300},
            {sick: 369, cured: 207},
            {sick: 250, cured: 344},
            {sick: 700, cured: 100},
            {sick: 120, cured: 400},
            {sick: 400, cured: 150},
            {sick: 366, cured: 211},
            {sick: 100, cured: 800},
            {sick: 200, cured: 500},
            {sick: 20, cured: 200},
            {sick: 650, cured: 480}
        ]
    },
    {
        id: 'cornelia_phillips',
        stats: [
            {sick: 97, cured: 154},
            {sick: 300, cured: 205},
            {sick: 207, cured: 369},
            {sick: 250, cured: 344},
            {sick: 500, cured: 100},
            {sick: 120, cured: 400},
            {sick: 400, cured: 150},
            {sick: 366, cured: 211},
            {sick: 100, cured: 500},
            {sick: 200, cured: 500},
            {sick: 20, cured: 200},
            {sick: 650, cured: 480}
        ]
    },
    {
        id: 'isaiah_goodman',
        stats: [
            {sick: 352, cured: 469},
            {sick: 488, cured: 610},
            {sick: 120, cured: 80},
            {sick: 250, cured: 344},
            {sick: 200, cured: 100},
            {sick: 120, cured: 400},
            {sick: 80, cured: 200},
            {sick: 300, cured: 400},
            {sick: 500, cured: 100},
            {sick: 200, cured: 156},
            {sick: 500, cured: 800},
            {sick: 700, cured: 1000}
        ]
    },
    {
        id: 'claudia_turner',
        stats: [
            {sick: 100, cured: 469},
            {sick: 488, cured: 610},
            {sick: 120, cured: 80},
            {sick: 250, cured: 344},
            {sick: 200, cured: 100},
            {sick: 120, cured: 400},
            {sick: 800, cured: 200},
            {sick: 300, cured: 400},
            {sick: 500, cured: 100},
            {sick: 200, cured: 156},
            {sick: 500, cured: 20},
            {sick: 100, cured: 50}
        ]
    },
    {
        id: 'emily_rivera',
        stats: [
            {sick: 800, cured: 400},
            {sick: 400, cured: 800},
            {sick: 250, cured: 500},
            {sick: 500, cured: 250},
            {sick: 800, cured: 400},
            {sick: 400, cured: 800},
            {sick: 250, cured: 500},
            {sick: 500, cured: 250},
            {sick: 800, cured: 400},
            {sick: 400, cured: 800},
            {sick: 250, cured: 500},
            {sick: 500, cured: 250},
        ]
    }
]