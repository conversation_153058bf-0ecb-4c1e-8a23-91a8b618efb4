import ReactDOM from "react-dom/client";
import App from "./App";
import { Provider } from "react-redux";
import store from "@store/store";
import { BrowserRouter } from "react-router-dom";
import { InterfaceContextAPI } from "@contexts/interfaceContext";

ReactDOM.createRoot(document.getElementById("root")).render(
  <Provider store={store}>
    <BrowserRouter>
      <InterfaceContextAPI>
        <App />
      </InterfaceContextAPI>
    </BrowserRouter>
  </Provider>,
);
