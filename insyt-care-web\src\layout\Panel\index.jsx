// styled components
import { Actions, Header, Input, Label, Search } from "./style";

// components
import Logo from "@ui/Logo";
import MenuButton from "@ui/MenuButton";
import ShapeButton from "@ui/ShapeButton";
import { motion } from "framer-motion";
import CurrentUser from "@layout/Panel/CurrentUser";

// hooks
import useWindowSize from "@hooks/useWindowSize";
import usePanelScroll from "@hooks/usePanelScroll";
import { useSidebarContext } from "@contexts/sidebarContext";
import { useRef, useEffect } from "react";
import { Box, Popover, Typography } from "@mui/material";
import { useState } from "react";
import styled from "styled-components";
import theme from "styled-theming";
import { colors, dark, flex, light } from "@styles/vars";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import NotificationItem from "@ui/NotificationItem";
import { markNotificationsAsRead } from "@store/slices/notifications";

const bg = theme("theme", {
  light: light.bodyBg,
  dark: dark.highlight,
});

export const Button = styled.button`
  width: 40px;
  aspect-ratio: 1;
  background-color: ${bg};
  color: ${colors.gray};
  ${flex.col}
  ${flex.center}
  position: relative;
  transition: color var(--transition), background-color var(--transition);

  &:hover,
  &:focus {
    background-color: ${colors.blue};
    color: #fff;
  }

  .badge {
    position: absolute;
  }

  &.square {
    border-radius: 8px;
  }

  &.round {
    border-radius: 50%;
  }
`;

const NotificationsContainerBg = theme("theme", {
  light: light.widgetBg,
  dark: dark.widgetBg,
});

const NotificationsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 0.7rem;
  background-color: ${NotificationsContainerBg};
  min-height: 40px;
  max-height: 400px;
  overflow-y: auto;
  width: 100%;
  min-width: 400px;
`;

const Panel = () => {
  const { width } = useWindowSize();
  const isMobile = width < 768;
  const isDesktop = width >= 1366;
  const classname = usePanelScroll();
  const { isSidebarOpen } = useSidebarContext();
  const headerRef = useRef(null);

  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  const { notifications, unreadCount = 0 } = useSelector((state) => state.notifications);
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const dispatch = useDispatch();

  // Fix: Define the id for the Popover
  const popoverId = open ? 'notifications-popover' : undefined;

  useEffect(() => {
    document.documentElement.style.setProperty("--header-height", `${headerRef.current.offsetHeight}px`);
  }, [width]);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
    // Mark notifications as read when panel is opened
    if (unreadCount > 0) {
      dispatch(markNotificationsAsRead());
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      <Header
        as={motion.header}
        animate={{ y: isSidebarOpen && isMobile ? "-100%" : 0 }}
        transition={{ duration: 0.3, ease: "linear", type: "tween" }}
        className={classname}
        ref={headerRef}
      >
        {!isDesktop && (
          <div className="logo-wrapper">
            <Logo compact={isMobile} />
          </div>
        )}
        {isMobile ? (
          <>
            <CurrentUser />
            <MenuButton />
          </>
        ) : (
          <Actions>
            <CurrentUser />
            {width < 1366 && <MenuButton />}
          </Actions>
        )}
      </Header>

      <Popover
        id={popoverId}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{ horizontal: "right" }}
        sx={{ mt: { xs: 3, lg: 0 } }}
      >
        <NotificationsContainer>
          {notifications?.length > 0 ? (
            notifications?.map((item, index) => <NotificationItem key={index} notification={item} />)
          ) : (
            <Typography sx={{ width: "fit-content", mx: "auto" }}>No notification yet..</Typography>
          )}
        </NotificationsContainer>
      </Popover>
    </>
  );
};

export default Panel;
