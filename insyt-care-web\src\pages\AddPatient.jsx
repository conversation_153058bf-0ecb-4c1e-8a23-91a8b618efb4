// components
import { Step, <PERSON><PERSON><PERSON><PERSON>, <PERSON>per } from "@mui/material";

// hooks
import Page from "@layout/Page";
import { useSearchParams } from "react-router-dom";
import { Box } from "@mui/material";
import styled from "styled-components";
import Widget from "@components/Widget";
import { useState } from "react";
import { text } from "@styles/global";
import AddPatientStep1 from "./AddPatientStep1";
import AddPatientStep2 from "./AddPatientStep2";
import AddPatientStep3 from "./AddPatientStep3";
import AddPatientStep4 from "./AddPatientStep4";
import { useEffect } from "react";
import { useSelector } from "react-redux";

const StyledStepperContainer = styled(Box)`
  .MuiStepLabel-label.Mui-active {
    color: ${text};
  }

  .MuiStep-root {
    &:hover .MuiStepLabel-root {
      &[style*="cursor: pointer"] {
        .MuiStepLabel-label {
          color: #1976d2;
        }
      }
    }
  }

  .MuiStepLabel-root[style*="cursor: pointer"] {
    .MuiStepLabel-label {
      transition: color 0.2s ease;
    }
  }
`;
const ResponsiveLabel = styled.div`
  display: none;

  /* Show only on large screens (e.g., 1024px and above) */
  @media (min-width: 1024px) {
    display: inline;
  }
`;

const steps = ["Step 1", "Step 2", "Step 3", "Step 4"];

const AddPatient = () => {
  const [searchParams] = useSearchParams();
  const client_id = searchParams.get("client_id");
  const { clients } = useSelector((state) => state.users);

  const [currentPatient, setCurrentPatient] = useState(null);

  const [activeStep, setActiveStep] = useState(0);

  function gotoNextStep() {
    setActiveStep((prev) => (prev < steps.length - 1 ? prev + 1 : prev));
  }

  function gotoPreviousStep() {
    setActiveStep((prev) => (prev > 0 ? prev - 1 : prev));
  }

  // Function to handle direct step navigation when editing
  function gotoStep(stepIndex) {
    if (client_id && currentPatient) {
      // When editing, allow navigation to any completed step or the next incomplete step
      const maxAllowedStep = getMaxAllowedStep();
      if (stepIndex <= maxAllowedStep) {
        setActiveStep(stepIndex);
      }
    }
  }

  // Determine the maximum step user can navigate to based on patient's onboard progress
  function getMaxAllowedStep() {
    if (!currentPatient) return 0;

    const onboardStep = currentPatient?.currentOnboardStep || 1;
    // Allow navigation to completed steps and the current step
    return Math.min(onboardStep - 1, steps.length - 1);
  }

  // Check if a step is clickable (only when editing and step is accessible)
  function isStepClickable(stepIndex) {
    if (!client_id || !currentPatient) return false;
    return stepIndex <= getMaxAllowedStep();
  }
  useEffect(() => {
    if (clients?.length && client_id) {
      const patient_found = clients?.find((item) => item.id === client_id);
      if (patient_found) {
        setCurrentPatient(patient_found);

        // Set the active step based on current onboard step when editing
        if (patient_found.currentOnboardStep) {
          // Set to the step they're currently on (currentOnboardStep is 1-indexed)
          const stepIndex = Math.min(patient_found.currentOnboardStep - 1, steps.length - 1);
          setActiveStep(stepIndex);
        }
      }
    }
  }, [clients, client_id]);

  return (
    <>
      <Page title={client_id ? "Edit Patient" : "Add Patient"}>
        <Widget>
          <Box p={{ xs: 2, md: 4 }}>
            {" "}
            {/* STEPPER */}{" "}
            <StyledStepperContainer sx={{ width: "100%", mb: 3 }}>
              <Stepper activeStep={activeStep}>
                {steps.map((label, index) => {
                  const stepProps = {};
                  const labelProps = {};
                  const isClickable = isStepClickable(index);
                  
                  const isCompleted = currentPatient && currentPatient.currentOnboardStep > index + 1;

                  return (
                    <Step
                      key={label}
                      {...stepProps}
                      completed={isCompleted}
                      title={!isClickable && client_id ? "Complete previous steps to access" : ""}
                      sx={{
                        cursor: isClickable ? "pointer" : "default",
                        "& .MuiStepLabel-root": {
                          cursor: isClickable ? "pointer" : "default",
                        },
                        "& .MuiStepLabel-label": {
                          opacity: isClickable ? 1 : 0.6,
                          fontWeight: isClickable ? 500 : 400,
                        },
                      }}
                      onClick={() => isClickable && gotoStep(index)}
                    >
                      <StepLabel
                        {...labelProps}
                        StepIconProps={{
                          sx: {
                            color: isClickable ? "primary.main" : "grey.400",
                            "&.Mui-active": {
                              color: "primary.main",
                            },
                            "&.Mui-completed": {
                              color: "success.main",
                            },
                          },
                        }}
                      >
                        <ResponsiveLabel>{label}</ResponsiveLabel>
                      </StepLabel>
                    </Step>
                  );
                })}
              </Stepper>
            </StyledStepperContainer>
            {/* STEP 1 */}
            {activeStep === 0 ? (
              <AddPatientStep1
                gotoNext={gotoNextStep}
                goBack={gotoPreviousStep}
                canGoBack={false}
                currentPatient={currentPatient}
                setCurrentPatient={setCurrentPatient}
              />
            ) : null}
            {/* STEP 2 */}
            {activeStep === 1 ? (
              <AddPatientStep2
                gotoNext={gotoNextStep}
                goBack={gotoPreviousStep}
                canGoBack
                currentPatient={currentPatient}
                setCurrentPatient={setCurrentPatient}
              />
            ) : null}
            {/* STEP 3 */}
            {activeStep === 2 ? (
              <AddPatientStep3
                gotoNext={gotoNextStep}
                goBack={gotoPreviousStep}
                canGoBack
                currentPatient={currentPatient}
                setCurrentPatient={setCurrentPatient}
              />
            ) : null}
            {/* STEP 4 */}
            {activeStep === 3 ? (
              <AddPatientStep4
                gotoNext={gotoNextStep}
                goBack={gotoPreviousStep}
                canGoBack
                currentPatient={currentPatient}
                setCurrentPatient={setCurrentPatient}
              />
            ) : null}
          </Box>
        </Widget>

        {/* ASSIGN SHIFT MODAL */}
        {/* <AssignShiftModal
          elemsHeight={0}
          mode="add_client"
          name={null}
          open={isScheduleOpen}
          handler={setAssignShiftOpen}
          date={moment().add(1, "days").format("YYYY-MM-DD")}
          client_options={[]}
          defaultValues={{ client: thisPatient?.id }}
          commonNurse={nurses.find((item) => item?.id === thisPatient?.assignedNurse)}
          caregiver_options={caregivers_list}
        /> */}

        {/* SCHEDULE APPOINTMENT MODAL */}
        {/* <ScheduleAppointmentModal
          data={{ name: "Caregiver 099" }}
          isVisible={isScheduleOpen}
          onCloseModal={() => setAssignShiftOpen(false)}
          caregiver_options={caregivers_list}
          client={thisPatient}
          mode="add_client"
        /> */}
      </Page>
    </>
  );
};

export default AddPatient;
