import React from "react";
import { StyledForm } from "@widgets/UserSettings/style";
import CustomSelect from "@ui/Select";
import { Button, CircularProgress, Divider, Grid } from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Typography } from "@mui/material";
import styled from "styled-components";
import { textSizes } from "@styles/vars";
import { useDispatch } from "react-redux";
import { useSnackbar } from "notistack";
import { doc, updateDoc } from "firebase/firestore";
import { db } from "@config/firebase.config";
import { COLLECTIONS } from "@constants/app";
import { updateUserAction } from "@store/slices/users";
import { useEffect } from "react";

const Label = styled.label`
  font-size: ${textSizes["14"]};
  width: fit-content;
  margin-bottom: 8px;
  display: block;
`;

const MOBILITY_OPTIONS = [
  { label: "Independent", value: "independent" },
  { label: "Needs Assistance", value: "needAssistance" },
  { label: "Bedbound", value: "bedbound" },
];
const BATHING_OPTIONS = [
  { label: "Independent", value: "independent" },
  { label: "Needs Help", value: "needHelp" },
  { label: "Unable", value: "unable" },
];
const TOILETING_OPTIONS = [
  { label: "Independent", value: "independent" },
  { label: "Needs Help", value: "needHelp" },
  { label: "Incontinent", value: "incontinent" },
];
const FEEDING_OPTIONS = [
  { label: "Independent", value: "independent" },
  { label: "Needs Help", value: "needHelp" },
  { label: "NPO", value: "npo" },
];
const YES_NO_OPTIONS = [
  { label: "Yes", value: true },
  { label: "No", value: false },
];
const EQUIPMENT_NEEDED_OPTIONS = [
  { label: "Wheelchair", value: "wheelchair" },
  { label: "Walker", value: "walker" },
  { label: "Commode", value: "commode" },
];
const ORIENTATION_OPTIONS = [
  { label: "Alert", value: "alert" },
  { label: "Confused", value: "confused" },
  { label: "Non-verbal", value: "nonVerbal" },
];
const MOOD_OPTIONS = [
  { label: "Stable", value: "stable" },
  { label: "Anxious", value: "anxious" },
  { label: "Depressed", value: "depressed" },
];
const FAMILY_SUPPORT_OPTIONS = [
  { label: "Full-time", value: "fullTime" },
  { label: "Part-time", value: "partTime" },
  { label: "None", value: "none" },
];

const step3Schema = z.object({
  mobility: z.string().nonempty("Required"),
  bathing: z.string().nonempty("Required"),
  dressing: z.string().nonempty("Required"),
  toileting: z.string().nonempty("Required"),
  feeding: z.string().nonempty("Required"),
  hasFallRisk: z.boolean({ message: "Required" }).default(null),
  presenceOfPetsSmokers: z.boolean({ message: "Required" }).default(null),
  equipmentNeeded: z.string().nonempty("Required"),
  orientation: z.string().nonempty("Required"),
  mood: z.string().nonempty("Required"),
  familySupport: z.string().nonempty("Required"),
  isCaregiverPresent: z.boolean({ message: "Required" }).default(null),
});

const MAX_DATE_DOB = new Date();
MAX_DATE_DOB.setFullYear(new Date().getFullYear() - 5);

const AddPatienStep3 = ({ gotoNext, goBack, canGoBack, setCurrentPatient, currentPatient }) => {
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();

  const {
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    setValue,
  } = useForm({
    defaultValues: {
      mobility: "",
      bathing: "",
      dressing: "",
      toileting: "",
      feeding: "",
      hasFallRisk: null,
      presenceOfPetsSmokers: null,
      equipmentNeeded: "",
      orientation: "",
      mood: "",
      familySupport: "",
      isCaregiverPresent: null,
    },
    resolver: zodResolver(step3Schema),
    mode: "all",
  });

  // * SUBMIT FORM
  async function submitForm(formValues) {
    if (currentPatient?.id) {
      const payload = {
        ...formValues,
        currentOnboardStep: 4,
        onboardPercentage: currentPatient?.onboardPercentage >= 75 ? currentPatient?.onboardPercentage : 75,
      };

      await updateDoc(doc(db, COLLECTIONS.USERS, currentPatient?.id), payload)
        .then(() => {
          const updated_patient = {
            ...currentPatient,
            ...payload,
          };
          dispatch(updateUserAction(updated_patient));
          enqueueSnackbar("Pateint's data updated", { variant: "success" });
          setCurrentPatient(updated_patient);
          gotoNext();
        })
        .catch(() => enqueueSnackbar("Couldn't update the patient's data", { variant: "error" }));
    }
  }

  useEffect(() => {
    if (currentPatient?.id) {
      currentPatient?.mobility && setValue("mobility", currentPatient?.mobility);
      currentPatient?.bathing && setValue("bathing", currentPatient?.bathing);
      currentPatient?.dressing && setValue("dressing", currentPatient?.dressing);
      currentPatient?.feeding && setValue("feeding", currentPatient?.feeding);
      currentPatient?.toileting && setValue("toileting", currentPatient?.toileting);
      setValue("hasFallRisk", Boolean(currentPatient?.hasFallRisk));
      setValue("presenceOfPetsSmokers", Boolean(currentPatient?.presenceOfPetsSmokers));
      currentPatient?.equipmentNeeded && setValue("equipmentNeeded", currentPatient?.equipmentNeeded);
      currentPatient?.orientation && setValue("orientation", currentPatient?.orientation);
      currentPatient?.mood && setValue("mood", currentPatient?.mood);
      currentPatient?.familySupport && setValue("familySupport", currentPatient?.familySupport);
      setValue("isCaregiverPresent", Boolean(currentPatient?.isCaregiverPresent));
    }
  }, [currentPatient]);

  return (
    <>
      <StyledForm onSubmit={handleSubmit(submitForm)}>
        <Grid container spacing={2}>
          <Grid size={12}>
            <Typography variant="h6" fontWeight={500} mt={2}>
              Functional & Safety Assessment
            </Typography>
          </Grid>

          {/* MOBILITY */}
          <Grid size={{ xs: 12, sm: 6 }}>
            <Controller
              name="mobility"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="mobility">Mobility</Label>
                  <CustomSelect
                    placeholder="Select"
                    options={MOBILITY_OPTIONS}
                    changeHandler={(option) => field.onChange(option.value)}
                    value={MOBILITY_OPTIONS.find((item) => item.value === field.value)}
                  />
                </>
              )}
            />
            {errors?.mobility?.message && (
              <Typography color="error" variant="caption">
                {errors?.mobility?.message}
              </Typography>
            )}
          </Grid>

          <Grid size={12} spacing={2}>
            <Typography fontWeight={500}>ADLs (Activities of Daily Living)</Typography>
          </Grid>

          {/* BATHING */}
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <Controller
              name="bathing"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="bathing">Bathing</Label>
                  <CustomSelect
                    placeholder="Select"
                    options={BATHING_OPTIONS}
                    changeHandler={(option) => field.onChange(option.value)}
                    value={BATHING_OPTIONS.find((item) => item.value === field.value)}
                  />
                </>
              )}
            />
            {errors?.bathing?.message && (
              <Typography color="error" variant="caption">
                {errors?.bathing?.message}
              </Typography>
            )}
          </Grid>

          {/* DRESSING */}
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <Controller
              name="dressing"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="Dressing">Dressing</Label>
                  <CustomSelect
                    placeholder="Select"
                    options={BATHING_OPTIONS}
                    changeHandler={(option) => field.onChange(option.value)}
                    value={BATHING_OPTIONS.find((item) => item.value === field.value)}
                  />
                </>
              )}
            />
            {errors?.dressing?.message && (
              <Typography color="error" variant="caption">
                {errors?.dressing?.message}
              </Typography>
            )}
          </Grid>

          {/* FEEDING */}
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <Controller
              name="feeding"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="feeding">Feeding</Label>
                  <CustomSelect
                    placeholder="Select"
                    options={FEEDING_OPTIONS}
                    changeHandler={(option) => field.onChange(option.value)}
                    value={FEEDING_OPTIONS.find((item) => item.value === field.value)}
                  />
                </>
              )}
            />
            {errors?.feeding?.message && (
              <Typography color="error" variant="caption">
                {errors?.feeding?.message}
              </Typography>
            )}
          </Grid>

          {/* TOILETTING */}
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <Controller
              name="toileting"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="toileting">Toileting</Label>
                  <CustomSelect
                    placeholder="Select"
                    options={TOILETING_OPTIONS}
                    changeHandler={(option) => field.onChange(option.value)}
                    value={TOILETING_OPTIONS.find((item) => item.value === field.value)}
                  />
                </>
              )}
            />
            {errors?.toileting?.message && (
              <Typography color="error" variant="caption">
                {errors?.toileting?.message}
              </Typography>
            )}
          </Grid>

          <Grid size={12} spacing={2}>
            <Typography fontWeight={500}>Home Environment</Typography>
          </Grid>

          {/* HAS FALL RISK */}
          <Grid size={{ xs: 12, sm: 4 }}>
            <Controller
              name="hasFallRisk"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="hasFallRisk">Any fall risks? (rugs, stairs, poor lighting)</Label>
                  <CustomSelect
                    placeholder="Select"
                    options={YES_NO_OPTIONS}
                    changeHandler={(option) => field.onChange(option.value)}
                    value={YES_NO_OPTIONS.find((item) => item.value === field.value)}
                  />
                </>
              )}
            />
            {errors?.hasFallRisk?.message && (
              <Typography color="error" variant="caption">
                {errors?.hasFallRisk?.message}
              </Typography>
            )}
          </Grid>

          {/* PRESENCE OF PETS/SMOKERS */}
          <Grid size={{ xs: 12, sm: 4 }}>
            <Controller
              name="presenceOfPetsSmokers"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="presenceOfPetsSmokers">Presence of Pets/Smokers</Label>
                  <CustomSelect
                    placeholder="Select"
                    options={YES_NO_OPTIONS}
                    changeHandler={(option) => field.onChange(option.value)}
                    value={YES_NO_OPTIONS.find((item) => item.value === field.value)}
                  />
                </>
              )}
            />
            {errors?.presenceOfPetsSmokers?.message && (
              <Typography color="error" variant="caption">
                {errors?.presenceOfPetsSmokers?.message}
              </Typography>
            )}
          </Grid>

          {/* EQUIPMENT NEEDED */}
          <Grid size={{ xs: 12, sm: 4 }}>
            <Controller
              name="equipmentNeeded"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="equipmentNeeded">Equipment Needed</Label>
                  <CustomSelect
                    placeholder="Select"
                    options={EQUIPMENT_NEEDED_OPTIONS}
                    changeHandler={(option) => field.onChange(option.value)}
                    value={EQUIPMENT_NEEDED_OPTIONS.find((item) => item.value === field.value)}
                  />
                </>
              )}
            />
            {errors?.equipmentNeeded?.message && (
              <Typography color="error" variant="caption">
                {errors?.equipmentNeeded?.message}
              </Typography>
            )}
          </Grid>
        </Grid>

        <Divider sx={{ my: 2, mt: 4 }} />

        <Grid container spacing={2}>
          <Grid size={12}>
            <Typography variant="h6" fontWeight={500} mt={1}>
              Psychosocial & Cognitive Status
            </Typography>
          </Grid>

          {/* ORIENTATION */}
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <Controller
              name="orientation"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="Orientation">Orientation</Label>
                  <CustomSelect
                    placeholder="Select"
                    options={ORIENTATION_OPTIONS}
                    changeHandler={(option) => field.onChange(option.value)}
                    value={ORIENTATION_OPTIONS.find((item) => item.value === field.value)}
                  />
                </>
              )}
            />
            {errors?.orientation?.message && (
              <Typography color="error" variant="caption">
                {errors?.orientation?.message}
              </Typography>
            )}
          </Grid>

          {/* MOOD */}
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <Controller
              name="mood"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="mood">Mood</Label>
                  <CustomSelect
                    placeholder="Select"
                    options={MOOD_OPTIONS}
                    changeHandler={(option) => field.onChange(option.value)}
                    value={MOOD_OPTIONS.find((item) => item.value === field.value)}
                  />
                </>
              )}
            />
            {errors?.mood?.message && (
              <Typography color="error" variant="caption">
                {errors?.mood?.message}
              </Typography>
            )}
          </Grid>

          {/* FAMILY SUPPORT */}
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <Controller
              name="familySupport"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="mobility">Family Support</Label>
                  <CustomSelect
                    placeholder="Select"
                    options={FAMILY_SUPPORT_OPTIONS}
                    changeHandler={(option) => field.onChange(option.value)}
                    value={FAMILY_SUPPORT_OPTIONS.find((item) => item.value === field.value)}
                  />
                </>
              )}
            />
            {errors?.familySupport?.message && (
              <Typography color="error" variant="caption">
                {errors?.familySupport?.message}
              </Typography>
            )}
          </Grid>

          {/* CAREGIVER PRESENT */}
          <Grid size={{ xs: 12, sm: 6, md: 3 }}>
            <Controller
              name="isCaregiverPresent"
              control={control}
              render={({ field }) => (
                <>
                  <Label htmlFor="mobility">Caregiver Present</Label>
                  <CustomSelect
                    placeholder="Select"
                    options={YES_NO_OPTIONS}
                    changeHandler={(option) => field.onChange(option.value)}
                    value={YES_NO_OPTIONS.find((item) => item.value === field.value)}
                  />
                </>
              )}
            />
            {errors?.isCaregiverPresent?.message && (
              <Typography color="error" variant="caption">
                {errors?.isCaregiverPresent?.message}
              </Typography>
            )}
          </Grid>

          <Grid
            size={12}
            sx={{
              display: "flex",
              marginTop: 8,
              justifyContent: "space-between",
              alignItems: "center",
              gap: 1.5,
            }}
          >
            <Button
              variant="outlined"
              color="primary"
              type="button"
              sx={{
                fontSize: 16,
                borderRadius: 2,
                textTransform: "none",
                fontWeight: 400,
                maxWidth: 200,
                width: "100%",
              }}
              disabled={!canGoBack || isSubmitting}
              onClick={goBack}
            >
              {"Back"}
            </Button>
            <Button
              variant="contained"
              color="primary"
              type="submit"
              sx={{
                fontSize: 16,
                borderRadius: 2,
                textTransform: "none",
                fontWeight: 400,
                maxWidth: 200,
                width: "100%",
              }}
              disabled={isSubmitting}
            >
              {"Next"}
              {isSubmitting ? <CircularProgress size={16} color="inherit" sx={{ marginLeft: 1 }} /> : null}
            </Button>
          </Grid>
        </Grid>
      </StyledForm>
    </>
  );
};

export default AddPatienStep3;
