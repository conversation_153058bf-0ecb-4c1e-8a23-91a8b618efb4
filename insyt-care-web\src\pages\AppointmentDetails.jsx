import React, { useEffect, useState } from "react";
import Page from "@layout/Page";
import { useParams, useSearchParams } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { Box, Grid, Typography, IconButton } from "@mui/material";
import { ArrowBack } from "@mui/icons-material";
import styled from "styled-components";
import theme from "styled-theming";
import Avatar from "@ui/Avatar";
import { getNameInitials } from "@utils/helpers";
import { useNavigate } from "react-router-dom";
import { ReactComponent as ClockIcon } from "@assets/Clock.svg";
import { ReactComponent as RecurrenceIcon } from "@assets/Recurrence.svg";
import { ReactComponent as TaskListIcon } from "@assets/TaskList.svg";
import { ReactComponent as CommentIcon } from "@assets/Comment.svg";
import { ReactComponent as CalendarIcon } from "@assets/Calendar.svg";
import moment from "moment";
import {
  getAppointmentsBySeries,
  formatSeriesInfo,
  isRecurringAppointment,
  getRecurringSeriesId,
} from "@utils/recurringAppointmentHelpers";

import AppointmentActionButtons from "@components/AppointmentActionButtons";
import AppointmentStatus from "@components/AppointmentListItem/AppointmentStatus";
import VisitNotesSidebar from "@components/VisitNotesSidebar";
import Mood from '../assets/mood.png';
import mobility from '../assets/mobility.png';
import appitete from '../assets/appitete.png'

const userCardbg = theme("theme", {
  light: "#f6fafd",
  dark: "#1a1f2b",
});
const userCardBorder = theme("theme", {
  light: "#e5e7eb",
  dark: "#2d313a",
});

const PageHeaderWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  gap: 16px;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
`;

const PageTitle = styled.h1`
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: ${theme("theme", {
    light: "#1f2937",
    dark: "#f9fafb",
  })};
`;

const ActionButtonsWrapper = styled.div`
  display: flex;
  gap: 12px;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: flex-start;
  }

  @media (max-width: 600px) {
    width: 100%;
  }
`;

const UserCard = styled.div`
  width: 100%;
  background-color: ${userCardbg};
  border: 1px solid ${userCardBorder};
  border-radius: 12px;
  padding: 18px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

const blueShiftInfoCardBg = theme("theme", {
  light: "rgb(238, 249, 255)",
  dark: "rgb(46, 49, 56)",
});
const blueCardBorder = theme("theme", {
  light: "rgb(177, 196, 250)",
  dark: "rgb(56, 61, 88)",
});

const BlueShifInfoCard = styled.div`
  width: 100%;
  padding: 24px;
  border-radius: 12px;
  gap: 12px;
  background: ${blueShiftInfoCardBg};
  border: 1px solid ${blueCardBorder};
  min-height: 150px;
`;

const yellowShiftInfoCardBg = theme("theme", {
  light: "rgba(255, 252, 238, 1)",
  dark: "rgb(45, 45, 42)",
});
const yellowCardBorder = theme("theme", {
  light: "rgba(250, 234, 177, 1)",
  dark: "rgb(88, 80, 56)",
});
const YellowShifInfoCard = styled.div`
  width: 100%;
  padding: 24px;
  border-radius: 12px;
  gap: 12px;
  background: ${yellowShiftInfoCardBg};
  border: 1px solid ${yellowCardBorder};
  height: 100%;
  min-height: 150px;
`;

const redShiftInfoCardBg = theme("theme", {
  light: "rgb(255, 238, 238)",
  dark: "rgb(45, 42, 42)",
});
const redCardBorder = theme("theme", {
  light: "rgb(250, 177, 177)",
  dark: "rgb(88, 61, 61)",
});
const RedShifInfoCard = styled.div`
  width: 100%;
  padding: 24px;
  border-radius: 12px;
  gap: 12px;
  background: ${redShiftInfoCardBg};
  border: 1px solid ${redCardBorder};
  height: 100%;
  min-height: 150px;
`;

const recurrenceChipBg = theme("theme", {
  light: "rgba(229, 234, 242, 1)",
  dark: "rgb(62, 62, 64)",
});
const activeRecurrenceChipBg = theme("theme", {
  light: "rgba(247, 181, 0, 0.1)",
  dark: "rgba(112, 82, 0, 0.27)",
});
const activeRecurrenceChipText = theme("theme", {
  light: "rgba(247, 181, 0, 1)",
  dark: "rgba(247, 181, 0, 1)",
});
const RecurrenceChip = styled.div`
  width: fit-content;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  background: ${(props) => (props?.active ? activeRecurrenceChipBg : recurrenceChipBg)};
  color: ${(props) => (props?.active ? activeRecurrenceChipText : "rgba(137, 149, 161, 1)")};
  .active {
    background-color: red !important;
  }
`;

const greenShiftInfoCardBg = theme("theme", {
  light: "rgb(244, 255, 250)",
  dark: "rgb(46, 55, 51)",
});
const greenCardBorder = theme("theme", {
  light: "rgb(174, 249, 218)",
  dark: "rgb(49, 89, 69)",
});
const GreenShifInfoCard = styled.div`
  width: 100%;
  padding: 24px;
  border-radius: 12px;
  gap: 12px;
  background: ${greenShiftInfoCardBg};
  border: 1px solid ${greenCardBorder};
  min-height: 150px;
`;

const grayShiftInfoCardBg = theme("theme", {
  light: "rgb(234, 236, 240)",
  dark: "rgb(50, 52, 53)",
});
const grayCardBorder = theme("theme", {
  light: "rgb(198, 198, 198)",
  dark: "rgb(101, 101, 101)",
});
const GrayShifInfoCard = styled.div`
  width: 100%;
  padding: 24px;
  border-radius: 12px;
  gap: 12px;
  background: ${grayShiftInfoCardBg};
  border: 1px solid ${grayCardBorder};
  min-height: 150px;
`;

const AppointmentDetails = () => {
  const { appointmentId } = useParams();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // State for visit notes sidebar
  const [isVisitNotesSidebarOpen, setIsVisitNotesSidebarOpen] = useState(false);

  const { user: logged_user } = useSelector((state) => state.auth);
  const { appointments } = useSelector((state) => state.appointments);
  const { clients, caregivers, nurses } = useSelector((state) => state.users);

  // Handler functions for visit notes sidebar
  const handleOpenVisitNotes = () => {
    setIsVisitNotesSidebarOpen(true);
  };

  const handleCloseVisitNotes = () => {
    setIsVisitNotesSidebarOpen(false);
  };

  // Find the current appointment first
  const current_appointment = appointments?.find((item) => item?.id === appointmentId);

  // Get next appointment from the same recurring series
  const getNextAppointment = () => {
    const seriesId = getRecurringSeriesId(current_appointment);
    if (!seriesId) {
      return null;
    }

    // Find the next appointment in the same series
    const currentDate = new Date(current_appointment.startDateTime);
    const seriesAppointments = getAppointmentsBySeries(appointments, seriesId);

    const nextAppointments = seriesAppointments
      ?.filter((apt) => new Date(apt.startDateTime) > currentDate && apt.status === "SCHEDULED")
      ?.sort((a, b) => new Date(a.startDateTime) - new Date(b.startDateTime));

    return nextAppointments?.[0] || null;
  };

  const nextAppointment = getNextAppointment();

  // Get series information if this is a recurring appointment
  const seriesId = getRecurringSeriesId(current_appointment);
  const seriesInfo = seriesId ? formatSeriesInfo(appointments, seriesId) : null;
  const client = clients?.find((item) => item?.id === current_appointment?.client);
  const caregiver = caregivers?.find((item) => item?.id === current_appointment?.caregiver);

  const findNurse = () => {
    if (logged_user?.role === "NURSE") {
      return logged_user;
    } else {
      return nurses?.find((item) => item?.id === current_appointment?.nurse);
    }
  };
  const nurse = findNurse();
  const renderShiftLengthText = () => {
    const startDateTime = moment(current_appointment?.startDateTime);
    const endDateTime = moment(current_appointment?.endDateTime);
    const shiftLength = moment.duration(endDateTime.diff(startDateTime)).asHours();

    if (shiftLength <= 1) {
      return `${Math.round(shiftLength * 10) / 10} hour`;
    } else {
      return `${Math.round(shiftLength * 10) / 10} hours`;
    }
  };

  const handleBackToCalendar = () => {
    const returnPatientId = searchParams.get("returnPatientId");
    if (returnPatientId) {
      navigate(`/appointments?patientId=${returnPatientId}`);
    } else {
      navigate("/appointments");
    }
  };

  return (
    <>
      <Page hasTitle={false}>
        <PageHeaderWrapper>
          <PageTitle>Appointment Details</PageTitle>
          {/* <Box sx={{ maxWidth: "calc(100% - 32px)", ml: "auto" }}>
                  <Box display="flex" gap={2} alignItems="center" justifyContent="space-between" mb={2}>
                    <Typography color="#8995A1" fontSize={"14px"}>
                      Status
                    </Typography>
                    <AppointmentStatus status={current_appointment?.status} />
                  </Box>
                </Box> */}
          <ActionButtonsWrapper>
            <AppointmentActionButtons
              appointment={current_appointment}
              onActionComplete={(action) => {
                console.log(`Appointment ${action}`);
                // Optionally refresh data or navigate
              }}
              onViewVisitNotes={handleOpenVisitNotes}
            />
          </ActionButtonsWrapper>
        </PageHeaderWrapper>

        <Grid container spacing={3}>
          <Grid size={{ xs: 12, sm: 6, md: 4 }}>
            <UserCard>
              <Avatar
                avatar={{ jpg: nurse?.photo?.url }}
                initals={getNameInitials(nurse?.firstName, nurse?.lastName)}
                size={60}
                isRounded
              />
              <Typography color="#3A7AFE" fontSize={"18px"} fontWeight={600} my={1}>
                Nurse
              </Typography>
              <Typography fontSize={"16px"} fontWeight={400} my={0.3}>
                {nurse?.name}
              </Typography>
              <Typography color="#9CA3AF" fontSize={"12px"} fontWeight={400}>
                {nurse?.email}
              </Typography>
            </UserCard>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 4 }}>
            <UserCard>
              <Avatar
                avatar={{ jpg: caregiver?.photo?.url }}
                initals={getNameInitials(caregiver?.firstName, caregiver?.lastName)}
                size={60}
                isRounded
              />
              <Typography color="#5D63B5" fontSize={"18px"} fontWeight={600} my={1}>
                Caregiver
              </Typography>
              <Typography fontSize={"16px"} fontWeight={400} my={0.3}>
                {caregiver?.name}
              </Typography>
              <Typography color="#9CA3AF" fontSize={"12px"} fontWeight={400}>
                {caregiver?.email}
              </Typography>
            </UserCard>
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 4 }}>
            <UserCard>
              <Avatar
                avatar={{ jpg: client?.photo?.url }}
                initals={getNameInitials(client?.firstName, client?.lastName)}
                size={60}
                isRounded
              />
              <Typography color="#00898C" fontSize={"18px"} fontWeight={600} my={1}>
                Patient
              </Typography>
              <Typography fontSize={"16px"} fontWeight={400} my={0.3}>
                {client?.name}
              </Typography>
              <Typography color="#9CA3AF" fontSize={"12px"} fontWeight={400}>
                {client?.email}
              </Typography>
            </UserCard>
          </Grid>
        </Grid>

        <Grid container spacing={3} mt={3}>
          <Grid size={{ xs: 12, sm: 6 }}>
            <BlueShifInfoCard>
              <Box display="flex" gap={2} alignItems="center" mb={1.5}>
                <ClockIcon />
                <Typography fontWeight="600">Shift Information</Typography>
              </Box>
              <Box sx={{ maxWidth: "calc(100% - 32px)", ml: "auto" }}>
                <Box display="flex" gap={2} alignItems="center" justifyContent="space-between">
                  <Typography color="#8995A1" fontSize={"14px"}>
                    Shift Length
                  </Typography>
                  <Typography fontSize={"14px"} fontWeight={500}>
                    {renderShiftLengthText()}
                  </Typography>
                </Box>
                <Box display="flex" gap={2} alignItems="center" justifyContent="space-between">
                  <Typography color="#8995A1" fontSize={"14px"}>
                    Date
                  </Typography>
                  <Typography fontSize={"14px"} fontWeight={500}>
                    {moment(current_appointment?.startDateTime).format("MMMM DD, YYYY")}
                  </Typography>
                </Box>
                <Box display="flex" gap={2} alignItems="center" justifyContent="space-between">
                  <Typography color="#8995A1" fontSize={"14px"}>
                    Time
                  </Typography>{" "}
                  <Typography fontSize={"14px"} fontWeight={500}>
                    {moment(current_appointment?.startDateTime).format("hh:mm A")} -{" "}
                    {moment(current_appointment?.endDateTime).format("hh:mm A")}
                    {current_appointment?.isCrossMidnight &&
                      moment(current_appointment?.endDateTime).format("YYYY-MM-DD") !==
                        moment(current_appointment?.startDateTime).format("YYYY-MM-DD") &&
                      " (+1 day)"}
                  </Typography>
                </Box>
              </Box>
            </BlueShifInfoCard>
          </Grid>

          {/* RECURRENCE */}
          <Grid size={{ xs: 12, sm: 6 }}>
            <YellowShifInfoCard>
              <Box display="flex" gap={2} alignItems="center" mb={2}>
                <RecurrenceIcon />
                <Typography fontWeight="600">Recurrence</Typography>
              </Box>
              <Box sx={{ maxWidth: "calc(100% - 32px)", ml: "auto" }}>
                {current_appointment?.recurrence ? (
                  <>
                    <Box display="flex" gap={2} alignItems="center" flexWrap="wrap" mb={2}>
                      <RecurrenceChip active={current_appointment?.originalRecurrence?.frequence === "daily"}>
                        Daily
                      </RecurrenceChip>
                      <RecurrenceChip active={current_appointment?.originalRecurrence?.frequence === "weekly"}>
                        Weekly
                      </RecurrenceChip>
                      <RecurrenceChip active={current_appointment?.originalRecurrence?.frequence === "monthly"}>
                        Monthly
                      </RecurrenceChip>
                    </Box>
                  </>
                ) : (
                  <Typography color="#8995A1" fontSize={"14px"}>
                    Single appointment (no recurrence)
                  </Typography>
                )}
              </Box>
            </YellowShifInfoCard>
          </Grid>

          {/* MESSAGE & STATUS FOR NON-RECURRING APPOINTMENTS */}
          {!seriesId && (
            <Grid size={{ xs: 12, sm: 6 }}>
              <GreenShifInfoCard>
                <Box display="flex" gap={2} alignItems="center" mb={2}>
                  <CommentIcon />
                  <Typography fontWeight="600">Appointment Details</Typography>
                </Box>
                <Box sx={{ maxWidth: "calc(100% - 32px)", ml: "auto" }}>
                  <Box display="flex" gap={2} alignItems="center" justifyContent="space-between" mb={2}>
                    <Typography color="#8995A1" fontSize={"14px"}>
                      Status
                    </Typography>
                    <AppointmentStatus status={current_appointment?.status} />
                  </Box>
                  <Box display="flex" gap={2} alignItems="flex-start" justifyContent="space-between">
                    <Typography color="#8995A1" fontSize={"14px"}>
                      Message
                    </Typography>
                    <Typography fontSize={"14px"} fontWeight={500} sx={{ textAlign: "right", maxWidth: "60%" }}>
                      {current_appointment?.comments || "No message provided"}
                    </Typography>
                  </Box>
                </Box>
              </GreenShifInfoCard>
            </Grid>
          )}

          {/* COMMENTS - Only show for recurring appointments */}
          {seriesId && (
            <Grid size={{ xs: 12, sm: 6 }}>
              <GrayShifInfoCard>
                <Box display="flex" gap={2} alignItems="center" mb={2}>
                  <CommentIcon />
                  <Typography fontWeight="600">Comments</Typography>
                </Box>
                <Box sx={{ maxWidth: "calc(100% - 32px)", ml: "auto", fontSize: 14 }}>
                  {current_appointment?.comments || "(Not provided)"}
                </Box>
              </GrayShifInfoCard>
            </Grid>
          )}
        </Grid>
      </Page>

      {/* Visit Notes Sidebar */}
      <VisitNotesSidebar
        open={isVisitNotesSidebarOpen}
        onClose={handleCloseVisitNotes}
        visitNotes={current_appointment?.visitNotes}
        appointment={current_appointment}
      />
    </>
  );
};

export default AppointmentDetails;
