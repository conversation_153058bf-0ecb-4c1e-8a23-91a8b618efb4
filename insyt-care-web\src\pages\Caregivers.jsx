import React, { useState } from "react";
import Page from "@layout/Page";
import { Box, Grid, Paper, Typography } from "@mui/material";
import Modal from "@mui/material/Modal";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import WidgetsLoader from "@components/WidgetsLoader";
import CaregiversList from "@widgets/CaregiversList";

const Caregivers = () => {
  const rows = [
    { id: 1, name: "<PERSON>", email: "<EMAIL>", joinedOn: "April 1, 2023" },
    { id: 2, name: "<PERSON>", email: "<EMAIL>", joinedOn: "March 23, 2020" },
    { id: 3, name: "<PERSON>", email: "<EMAIL>", joinedOn: "August 14, 2019" },
    { id: 4, name: "<PERSON>", email: "<EMAIL>", joinedOn: "November 19, 2023" },
  ];

  const [isVisible, setIsVisible] = useState(false);
  const navigate = useNavigate();
  const { isLoading } = useSelector((state) => state.users);

  return (
    <>
      <Page title="Caregivers List">
        {isLoading ? <WidgetsLoader /> : null}
        {!isLoading ? <CaregiversList variant="caregiver" /> : null}
      </Page>

      {/* USER DETAILS MODAL */}
      <Modal
        open={isVisible}
        onClose={() => setIsVisible(false)}
        sx={{
          zIndex: 20000,
          padding: "24px",
          maxHeight: "100vh",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          overflowY: "auto",
        }}
        componentsProps={{
          backdrop: {
            transitionDuration: 500,
            sx: {
              backgroundColor: "rgba(0, 0, 0, 0.7)",
            },
          },
        }}
        closeAfterTransition
      >
        <Paper sx={{ padding: 1, margin: "auto" }}>
          <Box padding={2}>
            <Typography variant="h6">User Details</Typography>
            <Grid container gap={2} marginTop={2}>
              <Grid size={{ md: 6, xs: 12, lg: 6 }}>
                <Typography variant="body1">Name: Steve Jobs</Typography>
              </Grid>
              <Grid size={{ md: 6, xs: 12, lg: 6 }}>
                <Typography variant="body1">Email: <EMAIL></Typography>
              </Grid>
              <Grid size={{ md: 6, xs: 12, lg: 6 }}>
                <Typography variant="body1">Joined On: April 1, 2023</Typography>
              </Grid>
              <Grid size={{ md: 6, xs: 12, lg: 6 }}>
                <Typography variant="body1">No. of Appointments Done: 200</Typography>
              </Grid>
            </Grid>
          </Box>
        </Paper>
      </Modal>
    </>
  );
};

export default Caregivers;
