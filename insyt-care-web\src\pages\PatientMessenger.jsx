// components
import Page from "@layout/Page";
import UserList from "@components/Messenger/UsersList";
import ModalWindow from "@components/ModalWindow";
import Tab from "react-bootstrap/Tab";
import Messenger from "@components/Messenger";

// hooks
import { useState } from "react";
import { useSearchParams } from "react-router-dom";
import { useEffect } from "react";

const PatientMessenger = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const chat_id = searchParams.get("chat_id");
  const [activeList, setActiveList] = useState("caregivers");

  const [selectedTab, setSelectedTab] = useState(chat_id || "");
  const [openModal, setOpenModal] = useState(false);
  const smallScreen = window.matchMedia("(max-width: 1038.98px)").matches;

  function onTabChange(id) {
    setSelectedTab(id);
    if (chat_id) {
      console.log("chat_id ", chat_id);
      setSearchParams({});
    }
  }

  function onChangeActiveList(val) {
    setActiveList(val);
    if (chat_id) {
      setSearchParams({});
    }
  }

  const handleModalClose = () => {
    setOpenModal(false);
    setSelectedTab("");
  };

  window.addEventListener("resize", () => {
    if (smallScreen) {
      handleModalClose();
    }
  });

  useEffect(() => {
    if (chat_id) {
      setSelectedTab(chat_id);
    } else {
      setSelectedTab("");
    }
  }, [activeList]);

  return (
    <Tab.Container transition={true} activeKey={selectedTab} onSelect={setSelectedTab}>
      <Page title="Messages">
        <div key="contacts-list">
          <UserList
            variant="nurse"
            user={selectedTab}
            onUserSelect={onTabChange}
            setModal={setOpenModal}
            activeChat={selectedTab}
            setActiveList={onChangeActiveList}
            activeList={activeList}
          />
        </div>
        <div key="messenger">
          {smallScreen ? (
            <ModalWindow isVisible={openModal} visibilityHandler={handleModalClose}>
              <Messenger variant="patient" user={selectedTab} activeChat={selectedTab} />
            </ModalWindow>
          ) : (
            <Messenger variant="patient" user={selectedTab} activeChat={selectedTab} />
          )}
        </div>
      </Page>
    </Tab.Container>
  );
};

export default PatientMessenger;
