// components
import Page from "@layout/Page";
import PatientsList from "@widgets/PatientsList";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";

const Patients = () => {
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  return (
    <Page
      title="Patients List"
      btnText="Add Patient"
      showRightBtn={user?.role === "ADMIN"}
      onClickBtn={() => navigate("/add_client")}
    >
      <PatientsList />
    </Page>
  );
};

export default Patients;
