// components
import { depsOptions } from "@constants/options";
import Page from "@layout/Page";
import StaffList from "@widgets/StaffList";
import AssignAdminModal from "@components/AssignAdminModal";
import { useState } from "react";
import { useNavigate } from "react-router";
import { useSelector } from "react-redux";
import { Button, Box } from "@mui/material";
import { AdminPanelSettings } from "@mui/icons-material";

const Staff = () => {
  const navigate = useNavigate();
  const [category, setCategory] = useState(depsOptions[0]);
  const [isAssignAdminModalOpen, setIsAssignAdminModalOpen] = useState(false);

  const { user } = useSelector((state) => state.auth);

  const selectedRole = () => {
    switch (category.value) {
      case "all":
        return { btnText: "Add User", url: "/add_staff" };
      case "nurse":
        return { btnText: "Add User", url: "/add_staff" };
      case "caregiver":
        return { btnText: "Add User", url: "/add_staff" };
      default:
        return { btnText: "Add User", url: "/add_staff" };
    }
  };

  return (
    <>
      <Page
        title="Staff List"
        btnText={selectedRole()?.btnText}
        showRightBtn
        onClickBtn={() => navigate(selectedRole()?.url)}
        customActions={
          user?.role === "ADMIN" ? (
            <Box sx={{ ml: 2 }}>
              <Button
                variant="outlined"
                onClick={() => setIsAssignAdminModalOpen(true)}
                sx={{
                  borderColor: "primary",
                  color: "primary",
                  "&:hover": {
                    borderColor: "primary",
                    backgroundColor: "rgba(255, 107, 53, 0.04)",
                  },
                  "@media (max-width:425px)": {
                    lineHeight: "1.1px",
                    padding: "18px 7px",
                  },
                  "@media (max-width:320px)": {
                    lineHeight: "14.1px",
                    padding: "5px 7px",
                  },
                }}
              >
                Create Admin
              </Button>
            </Box>
          ) : null
        }
      >
        <StaffList variant="staff" category={category} setCategory={setCategory} />
      </Page>

      {/* Admin Assignment Modal */}
      <AssignAdminModal isVisible={isAssignAdminModalOpen} onClose={() => setIsAssignAdminModalOpen(false)} />
    </>
  );
};

export default Staff;
