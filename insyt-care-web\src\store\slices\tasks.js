import { db } from "config/firebase.config";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { collection, doc, getDocs, orderBy, query, setDoc } from "firebase/firestore";
import { COLLECTIONS } from "@constants/app";

const initialState = {
  error: null,
  isLoading: false,
  tasks: [],
  tasksDetails: null,
  currentNurse: null,
  currentCaregiver: null,
  currentPatient: null,
};

// THUNK TO GET ALL TASKS
export const getAllTasks = createAsyncThunk("tasks/getAllTasks", async (_, { rejectWithValue, fulfillWithValue }) => {
  try {
    const q = query(collection(db, COLLECTIONS.TASKS), orderBy("createdAt", "desc"));
    const snapshot = await getDocs(q);
    const arr = snapshot.docs.map((item) => ({ id: item.id, ...item.data() }));
    return fulfillWithValue(arr);
  } catch (error) {
    console.log("GET ALL TASKS THUNK >> ", error);
    return rejectWithValue(error);
  }
});

// THUNK TO CREATE NEW TASK DOC
export const createNewTaskDocThunk = createAsyncThunk(
  "tasks/createNewTaskDoc",
  async (payload, { rejectWithValue, fulfillWithValue }) => {
    try {
      const res = await setDoc(doc(db, COLLECTIONS.TASKS, payload.id), payload);
      return fulfillWithValue(res);
    } catch (error) {
      console.log("CREATE NEW TASK DOC THUNK >> ", error);
      return rejectWithValue(error);
    }
  },
);

const taskSlice = createSlice({
  name: "tasks",
  initialState,
  reducers: {
    addNewTaskAction: (state, { payload }) => {
      state.tasks = [payload, ...state.tasks];
      return state;
    },
    // addNewTaskDocAction: (state, { payload }) => {
    //   state.tasks = [...state.tasks, payload];
    //   state.tasksDetails = payload;
    //   return state;
    // },
    // addNewTaskToList: (state, { payload }) => {
    //   state.tasksDetails = {
    //     ...state.tasksDetails,
    //     tasks: [...state.tasksDetails?.tasks, payload],
    //   };
    //   return state;
    // },
    // setTaskDetailsAction: (state, { payload }) => {
    //   if (payload?.new) {
    //     state.tasksDetails = payload;
    //   } else {
    //     const taskDetails = state.tasks.find((task) => task?.id === payload);
    //     if (taskDetails) {
    //       state.tasksDetails = taskDetails;
    //     }
    //   }
    //   return state;
    // },
    // updateCurrentUserAction: (state, { payload }) => {
    //   if (payload?.role === "NURSE") {
    //     state.currentNurse = payload?.id;
    //   } else if (payload?.role === "CAREGIVER") {
    //     state.currentCaregiver = payload?.id;
    //   } else if (payload?.role === "CLIENT") {
    //     state.currentPatient = payload?.id;
    //   }
    //   if (state.currentNurse && state.currentPatient) {
    //     const taskDetails = state.tasks.find((item) => item?.client === state.currentPatient);
    //     console.log("task.details >> ", taskDetails);
    //     state.tasksDetails = taskDetails;
    //   } else {
    //     state.tasksDetails = null;
    //   }
    //   return state;
    // },
    // updateTaskAction: (state, { payload }) => {
    //   const { id, ...rest } = payload;
    //   const index = state.tasks.findIndex((task) => task.id === id);
    //   if (index !== -1) {
    //     state.tasks[index] = { ...state.tasks[index], ...rest };
    //   }
    //   if (state.currentNurse && state.currentCaregiver && state.currentPatient) {
    //     state.filteredTasks = state.tasks.filter(
    //       (task) =>
    //         task?.nurse === state.currentNurse &&
    //         task?.caregiver === state.currentCaregiver &&
    //         task?.client === state.currentPatient,
    //     );
    //   }
    //   return state;
    // },
  },
  extraReducers: (builder) => {
    builder
      // GET ALL TASKS
      .addCase(getAllTasks.pending, (state) => {
        state.error = null;
        state.isLoading = true;
      })
      .addCase(getAllTasks.fulfilled, (state, { payload }) => {
        state.tasks = payload;
        state.isLoading = false;
      })
      .addCase(getAllTasks.rejected, (state, { payload }) => {
        state.isLoading = false;
        state.error = payload;
      })
      // THUNK TO CREATE NEW TASK DOC
      .addCase(createNewTaskDocThunk.pending, (state) => {})
      .addCase(createNewTaskDocThunk.fulfilled, (state, { payload }) => {
        state.tasks = [...state.tasks, payload];
        state.tasksDetails = payload;
      })
      .addCase(createNewTaskDocThunk.rejected, (state, { payload }) => {});
  },
});

export const { addNewTaskAction } = taskSlice.actions;

export default taskSlice;
