import { configureStore } from "@reduxjs/toolkit";
import LayoutsReducer from "./slices/layout";
import TodosReducer from "./slices/todos";
import CardsReducer from "./slices/cards";
import MessengerReducer from "./slices/messenger";
import authSlice from "./slices/auth";
import usersSlice from "./slices/users";
import chatSlice from "./slices/chats";
import appointmentSlice from "./slices/appointments";
import taskSlice from "./slices/tasks";
import notificationSlice from "./slices/notifications";

const store = configureStore({
  reducer: {
    layout: LayoutsReducer,
    todos: TodosReducer,
    cards: CardsReducer,
    messenger: MessengerReducer,
    auth: authSlice.reducer,
    users: usersSlice.reducer,
    chats: chatSlice.reducer,
    appointments: appointmentSlice.reducer,
    tasks: taskSlice.reducer,
    notifications: notificationSlice.reducer,
  },
  middleware: (getDefaultMiddleware) => getDefaultMiddleware({ serializableCheck: false }),
  devTools: true,
});

export default store;
