import moment from 'moment';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '@config/firebase.config';
import { COLLECTIONS } from '@constants/app';

/**
 * Checks if two time ranges overlap, properly handling cross-midnight scenarios
 * @param {string} startTime1 - Start time of first range (HH:mm format)
 * @param {string} endTime1 - End time of first range (HH:mm format)
 * @param {string} startTime2 - Start time of second range (HH:mm format)
 * @param {string} endTime2 - End time of second range (HH:mm format)
 * @param {boolean} isCrossMidnight1 - Whether first appointment crosses midnight
 * @param {boolean} isCrossMidnight2 - Whether second appointment crosses midnight
 * @returns {boolean} True if time ranges overlap
 */
export const doTimeRangesOverlap = (startTime1, endTime1, startTime2, endTime2, isCrossMidnight1 = false, isCrossMidnight2 = false) => {
  // Parse times as moments on the same day for comparison
  let start1 = moment(startTime1, 'HH:mm');
  let end1 = moment(endTime1, 'HH:mm');
  let start2 = moment(startTime2, 'HH:mm');
  let end2 = moment(endTime2, 'HH:mm');

  // Auto-detect cross-midnight if not explicitly provided
  if (!isCrossMidnight1 && end1.isBefore(start1)) {
    isCrossMidnight1 = true;
  }
  if (!isCrossMidnight2 && end2.isBefore(start2)) {
    isCrossMidnight2 = true;
  }

  // Handle cross-midnight appointments by extending end times to next day
  if (isCrossMidnight1) {
    end1.add(1, 'day');
  }
  if (isCrossMidnight2) {
    end2.add(1, 'day');
  }

  // For cross-midnight appointments, we need to check overlaps in multiple scenarios
  if (isCrossMidnight1 || isCrossMidnight2) {
    // Direct overlap check with extended times
    const directOverlap = start1.isBefore(end2) && start2.isBefore(end1);
    
    // Special case: One appointment crosses midnight, the other doesn't
    if (isCrossMidnight1 && !isCrossMidnight2) {
      // Check if the regular appointment overlaps with the next-day portion
      const nextDayStart1 = moment('00:00', 'HH:mm');
      const nextDayEnd1 = moment(endTime1, 'HH:mm');
      const nextDayOverlap = nextDayStart1.isBefore(end2) && start2.isBefore(nextDayEnd1);
      return directOverlap || nextDayOverlap;
    }
    
    if (!isCrossMidnight1 && isCrossMidnight2) {
      // Check if the regular appointment overlaps with the next-day portion
      const nextDayStart2 = moment('00:00', 'HH:mm');
      const nextDayEnd2 = moment(endTime2, 'HH:mm');
      const nextDayOverlap = nextDayStart2.isBefore(end1) && start1.isBefore(nextDayEnd2);
      return directOverlap || nextDayOverlap;
    }
    
    // Both appointments cross midnight - use direct overlap with extended times
    return directOverlap;
  }

  // Standard overlap check for regular appointments (neither crosses midnight)
  return start1.isBefore(end2) && start2.isBefore(end1);
};

/**
 * Gets all existing appointments for a patient
 * @param {string} clientId - Patient/client ID
 * @param {string} excludeAppointmentId - Optional appointment ID to exclude from check
 * @param {string} excludeRefID - Optional refID to exclude entire recurring series from check
 * @returns {Promise<Array>} Array of existing appointments
 */
export const getExistingAppointmentsForPatient = async (clientId, excludeAppointmentId = null, excludeRefID = null) => {
  try {
    if (!clientId) {
      return [];
    }

    const q = query(
      collection(db, COLLECTIONS.APPOINTMENTS),
      where('client', '==', clientId),
      where('status', '==', 'SCHEDULED')
    );

    const snapshot = await getDocs(q);

    const appointments = snapshot.docs
      .map(doc => ({ id: doc.id, ...doc.data() }))
      .filter(appointment => {
        // Exclude the current appointment if editing
        if (excludeAppointmentId && appointment.id === excludeAppointmentId) {
          return false;
        }
        // Exclude entire recurring series if refID is provided
        if (excludeRefID && appointment.refID === excludeRefID) {
          return false;
        }
        return true;
      });

    return appointments;
  } catch (error) {
    console.error('Error fetching existing patient appointments:', error);
    throw error;
  }
};

/**
 * Gets all existing appointments for a caregiver
 * @param {string} caregiverId - Caregiver ID
 * @param {string} excludeAppointmentId - Optional appointment ID to exclude from check
 * @returns {Promise<Array>} Array of existing appointments
 */
export const getExistingAppointmentsForCaregiver = async (caregiverId, excludeAppointmentId = null) => {
  try {
    if (!caregiverId) {
      return [];
    }

    const q = query(
      collection(db, COLLECTIONS.APPOINTMENTS),
      where('caregiver', '==', caregiverId),
      where('status', '==', 'SCHEDULED')
    );

    const snapshot = await getDocs(q);

    const appointments = snapshot.docs
      .map(doc => ({ id: doc.id, ...doc.data() }))
      .filter(appointment => {
        // Exclude the current appointment if editing
        if (excludeAppointmentId && appointment.id === excludeAppointmentId) {
          return false;
        }
        return true;
      });

    return appointments;
  } catch (error) {
    console.error('Error fetching existing caregiver appointments:', error);
    throw error;
  }
};

/**
 * Main function to detect appointment time conflicts for both patient and caregiver
 * @param {string} clientId - Patient/client ID
 * @param {string} caregiverId - Caregiver ID
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} startTime - Start time in HH:mm format
 * @param {string} endTime - End time in HH:mm format
 * @param {string} excludeAppointmentId - Optional appointment ID to exclude from check
 * @param {string} excludeRefID - Optional refID to exclude entire recurring series from check
 * @returns {Promise<Object>} Object with conflict status and details
 */
export const checkAppointmentTimeConflicts = async (clientId, caregiverId, date, startTime, endTime, excludeAppointmentId = null, excludeRefID = null) => {
  try {
    if (!clientId || !caregiverId || !date || !startTime || !endTime) {
      return {
        hasConflict: false,
        patientConflicts: [],
        caregiverConflicts: []
      };
    }

    // Check if the new appointment crosses midnight
    const isCrossMidnight = moment(endTime, 'HH:mm').isBefore(moment(startTime, 'HH:mm'));
    const nextDate = moment(date).add(1, 'day').format('YYYY-MM-DD');

    // Get all existing appointments for patient and caregiver
    const [patientAppointments, caregiverAppointments] = await Promise.all([
      getExistingAppointmentsForPatient(clientId, excludeAppointmentId, excludeRefID),
      getExistingAppointmentsForCaregiver(caregiverId, excludeAppointmentId)
    ]);    // Helper function to check conflicts considering cross-midnight scenarios
    const checkConflicts = (appointments) => {
      return appointments.filter(appointment => {
        const existingStartDateTime = moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm');
        const existingEndDateTime = moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm');
        const existingStartDate = existingStartDateTime.format('YYYY-MM-DD');
        const existingEndDate = existingEndDateTime.format('YYYY-MM-DD');
        const existingStartTime = existingStartDateTime.format('HH:mm');
        const existingEndTime = existingEndDateTime.format('HH:mm');
        
        // Check if existing appointment crosses midnight
        // Look for both isCrossMidnight flag and date difference
        const existingIsCrossMidnight = appointment.isCrossMidnight || 
          (existingEndDate !== existingStartDate);

        // Case 1: New appointment crosses midnight
        if (isCrossMidnight) {
          // Check conflicts on the start date
          const conflictsOnStartDate = existingStartDate === date && 
            doTimeRangesOverlap(startTime, endTime, existingStartTime, existingEndTime, true, existingIsCrossMidnight);
          
          // Check conflicts on the end date (next day)
          const conflictsOnEndDate = existingStartDate === nextDate && 
            doTimeRangesOverlap(startTime, endTime, existingStartTime, existingEndTime, true, existingIsCrossMidnight);
          
          // Also check if existing appointment starts on start date but ends on next day
          const conflictsAcrossDays = existingIsCrossMidnight && existingStartDate === date &&
            doTimeRangesOverlap(startTime, endTime, existingStartTime, existingEndTime, true, true);
            
          return conflictsOnStartDate || conflictsOnEndDate || conflictsAcrossDays;
        }
        
        // Case 2: New appointment is regular (doesn't cross midnight)
        else {
          // Check if existing appointment is on the same date
          if (existingStartDate === date) {
            return doTimeRangesOverlap(startTime, endTime, existingStartTime, existingEndTime, false, existingIsCrossMidnight);
          }
          
          // Check if existing cross-midnight appointment ends on our date
          if (existingIsCrossMidnight && existingEndDate === date) {
            // The existing appointment started yesterday and ends today
            // We need to check if our appointment conflicts with the portion that ends today
            return doTimeRangesOverlap(startTime, endTime, '00:00', existingEndTime, false, false);
          }
          
          return false;
        }
      });
    };

    const patientConflicts = checkConflicts(patientAppointments);
    const caregiverConflicts = checkConflicts(caregiverAppointments);

    const hasConflict = patientConflicts.length > 0 || caregiverConflicts.length > 0;

    return {
      hasConflict,
      patientConflicts: patientConflicts.map(appointment => ({
        id: appointment.id,
        startTime: moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm'),
        endTime: moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm'),
        date: moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD'),
        endDate: moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD')
      })),
      caregiverConflicts: caregiverConflicts.map(appointment => ({
        id: appointment.id,
        startTime: moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm'),
        endTime: moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm'),
        date: moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD'),
        endDate: moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD'),
        clientId: appointment.client
      }))
    };
  } catch (error) {
    console.error('Error checking appointment time conflicts:', error);
    throw new Error(`Failed to validate appointment times: ${error.message}`);
  }
};

/**
 * Formats patient conflict error message for display
 * @param {Array} patientConflicts - Array of conflicting patient appointments
 * @returns {string} Formatted error message
 */
export const formatPatientConflictErrorMessage = (patientConflicts) => {
  if (patientConflicts.length === 0) {
    return '';
  }

  if (patientConflicts.length === 1) {
    const appointment = patientConflicts[0];
    const isCrossMidnight = appointment.endDate !== appointment.date;
    
    if (isCrossMidnight) {
      return `This patient already has an appointment from ${appointment.startTime} (${appointment.date}) to ${appointment.endTime} (${appointment.endDate}).`;
    } else {
      return `This patient already has an appointment from ${appointment.startTime} to ${appointment.endTime} on ${appointment.date}.`;
    }
  }

  const timeRanges = patientConflicts
    .map(appointment => {
      const isCrossMidnight = appointment.endDate !== appointment.date;
      if (isCrossMidnight) {
        return `${appointment.startTime} (${appointment.date}) to ${appointment.endTime} (${appointment.endDate})`;
      } else {
        return `${appointment.startTime}-${appointment.endTime} (${appointment.date})`;
      }
    })
    .join(', ');

  return `This patient already has appointments during these times: ${timeRanges}. Please choose a different time slot.`;
};

/**
 * Formats caregiver conflict error message for display
 * @param {Array} caregiverConflicts - Array of conflicting caregiver appointments
 * @returns {string} Formatted error message
 */
export const formatCaregiverConflictErrorMessage = (caregiverConflicts) => {
  if (caregiverConflicts.length === 0) {
    return '';
  }

  if (caregiverConflicts.length === 1) {
    const appointment = caregiverConflicts[0];
    const isCrossMidnight = appointment.endDate !== appointment.date;
    
    if (isCrossMidnight) {
      return `This caregiver already has an appointment from ${appointment.startTime} (${appointment.date}) to ${appointment.endTime} (${appointment.endDate}).`;
    } else {
      return `This caregiver already has an appointment from ${appointment.startTime} to ${appointment.endTime} on ${appointment.date}.`;
    }
  }

  const timeRanges = caregiverConflicts
    .map(appointment => {
      const isCrossMidnight = appointment.endDate !== appointment.date;
      if (isCrossMidnight) {
        return `${appointment.startTime} (${appointment.date}) to ${appointment.endTime} (${appointment.endDate})`;
      } else {
        return `${appointment.startTime}-${appointment.endTime} (${appointment.date})`;
      }
    })
    .join(', ');

  return `This caregiver already has appointments during these times: ${timeRanges}. Please choose a different time slot.`;
};



/**
 * @deprecated Use checkAppointmentTimeConflicts instead
 * Legacy function for patient appointment conflict checking
 */
export const checkAppointmentConflict = async (clientId, date, startTime, endTime, excludeAppointmentId = null, excludeRefID = null) => {
  try {
    if (!clientId || !startTime || !endTime) {
      return { hasConflict: false, conflictingAppointments: [] };
    }

    const patientAppointments = await getExistingAppointmentsForPatient(clientId, excludeAppointmentId, excludeRefID);
    
    // Check if the new appointment crosses midnight
    const isCrossMidnight = moment(endTime, 'HH:mm').isBefore(moment(startTime, 'HH:mm'));
    const nextDate = moment(date).add(1, 'day').format('YYYY-MM-DD');

    const patientConflicts = patientAppointments.filter(appointment => {
      const existingStartDateTime = moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm');
      const existingEndDateTime = moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm');
      const existingStartDate = existingStartDateTime.format('YYYY-MM-DD');
      const existingEndDate = existingEndDateTime.format('YYYY-MM-DD');
      const existingStartTime = existingStartDateTime.format('HH:mm');
      const existingEndTime = existingEndDateTime.format('HH:mm');
      
      // Check if existing appointment crosses midnight
      const existingIsCrossMidnight = appointment.isCrossMidnight || 
        (existingEndDate !== existingStartDate);

      // Case 1: New appointment crosses midnight
      if (isCrossMidnight) {
        // Check conflicts on the start date
        const conflictsOnStartDate = existingStartDate === date && 
          doTimeRangesOverlap(startTime, endTime, existingStartTime, existingEndTime, true, existingIsCrossMidnight);
        
        // Check conflicts on the end date (next day)
        const conflictsOnEndDate = existingStartDate === nextDate && 
          doTimeRangesOverlap(startTime, endTime, existingStartTime, existingEndTime, true, existingIsCrossMidnight);
        
        // Also check if existing appointment starts on start date but ends on next day
        const conflictsAcrossDays = existingIsCrossMidnight && existingStartDate === date &&
          doTimeRangesOverlap(startTime, endTime, existingStartTime, existingEndTime, true, true);
          
        return conflictsOnStartDate || conflictsOnEndDate || conflictsAcrossDays;
      }
      
      // Case 2: New appointment is regular (doesn't cross midnight)
      else {
        // Check if existing appointment is on the same date
        if (existingStartDate === date) {
          return doTimeRangesOverlap(startTime, endTime, existingStartTime, existingEndTime, false, existingIsCrossMidnight);
        }
        
        // Check if existing cross-midnight appointment ends on our date
        if (existingIsCrossMidnight && existingEndDate === date) {
          // The existing appointment started yesterday and ends today
          // We need to check if our appointment conflicts with the portion that ends today
          return doTimeRangesOverlap(startTime, endTime, '00:00', existingEndTime, false, false);
        }
        
        return false;
      }
    });

    return {
      hasConflict: patientConflicts.length > 0,
      conflictingAppointments: patientConflicts.map(appointment => ({
        id: appointment.id,
        startTime: moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm'),
        endTime: moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm'),
        date: moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD'),
        endDate: moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD')
      }))
    };
  } catch (error) {
    console.error('Error checking patient appointment conflict:', error);
    throw new Error(`Failed to validate patient appointment: ${error.message}`);
  }
};

/**
 * @deprecated Use checkAppointmentTimeConflicts instead
 * Legacy function for caregiver appointment conflict checking
 */
export const checkCaregiverAppointmentConflict = async (caregiverId, date, startTime, endTime, excludeAppointmentId = null) => {
  try {
    if (!caregiverId || !startTime || !endTime) {
      return { hasConflict: false, conflictingAppointments: [] };
    }

    const caregiverAppointments = await getExistingAppointmentsForCaregiver(caregiverId, excludeAppointmentId);
    
    // Check if the new appointment crosses midnight
    const isCrossMidnight = moment(endTime, 'HH:mm').isBefore(moment(startTime, 'HH:mm'));
    const nextDate = moment(date).add(1, 'day').format('YYYY-MM-DD');

    const caregiverConflicts = caregiverAppointments.filter(appointment => {
      const existingStartDateTime = moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm');
      const existingEndDateTime = moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm');
      const existingStartDate = existingStartDateTime.format('YYYY-MM-DD');
      const existingEndDate = existingEndDateTime.format('YYYY-MM-DD');
      const existingStartTime = existingStartDateTime.format('HH:mm');
      const existingEndTime = existingEndDateTime.format('HH:mm');
        // Check if existing appointment crosses midnight
      const existingIsCrossMidnight = appointment.isCrossMidnight || 
        (existingEndDate !== existingStartDate);

      // Case 1: New appointment crosses midnight
      if (isCrossMidnight) {
        // Check conflicts on the start date
        const conflictsOnStartDate = existingStartDate === date && 
          doTimeRangesOverlap(startTime, endTime, existingStartTime, existingEndTime, true, existingIsCrossMidnight);
        
        // Check conflicts on the end date (next day)
        const conflictsOnEndDate = existingStartDate === nextDate && 
          doTimeRangesOverlap(startTime, endTime, existingStartTime, existingEndTime, true, existingIsCrossMidnight);
        
        // Also check if existing appointment starts on start date but ends on next day
        const conflictsAcrossDays = existingIsCrossMidnight && existingStartDate === date &&
          doTimeRangesOverlap(startTime, endTime, existingStartTime, existingEndTime, true, true);
          
        return conflictsOnStartDate || conflictsOnEndDate || conflictsAcrossDays;
      }
      
      // Case 2: New appointment is regular (doesn't cross midnight)
      else {
        // Check if existing appointment is on the same date
        if (existingStartDate === date) {
          return doTimeRangesOverlap(startTime, endTime, existingStartTime, existingEndTime, false, existingIsCrossMidnight);
        }
        
        // Check if existing cross-midnight appointment ends on our date
        if (existingIsCrossMidnight && existingEndDate === date) {
          // The existing appointment started yesterday and ends today
          // We need to check if our appointment conflicts with the portion that ends today
          return doTimeRangesOverlap(startTime, endTime, '00:00', existingEndTime, false, false);
        }
        
        return false;
      }
    });

    return {
      hasConflict: caregiverConflicts.length > 0,
      conflictingAppointments: caregiverConflicts.map(appointment => ({
        id: appointment.id,
        startTime: moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm'),
        endTime: moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm'),
        date: moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD'),
        endDate: moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD'),
        clientId: appointment.client
      }))
    };
  } catch (error) {
    console.error('Error checking caregiver appointment conflict:', error);
    throw new Error(`Failed to validate caregiver appointment: ${error.message}`);
  }
};

/**
 * @deprecated Use formatPatientConflictErrorMessage instead
 * Legacy function for formatting conflict error messages
 */
export const formatConflictErrorMessage = (conflictingAppointments) => {
  return formatPatientConflictErrorMessage(conflictingAppointments);
};

/**
 * @deprecated Use checkForExactTimeDuplicate instead
 * Legacy function for checking exact duplicates (backward compatibility)
 */
export const checkForExactDuplicate = async (clientId, caregiverId, date, startTime, endTime, excludeAppointmentId = null) => {
  return await checkForExactTimeDuplicate(clientId, caregiverId, date, startTime, endTime, excludeAppointmentId);
};

/**
 * Checks for exact time duplicate appointments (same client, caregiver, date and time)
 * @param {string} clientId - Patient/client ID
 * @param {string} caregiverId - Caregiver ID
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} startTime - Start time in HH:mm format
 * @param {string} endTime - End time in HH:mm format
 * @param {string} excludeAppointmentId - Optional appointment ID to exclude from check
 * @returns {Promise<boolean>} True if exact time duplicate exists
 */
export const checkForExactTimeDuplicate = async (clientId, caregiverId, date, startTime, endTime, excludeAppointmentId = null) => {
  try {
    if (!clientId || !caregiverId || !date || !startTime || !endTime) {
      return false;
    }

    // Query for appointments with same client and caregiver
    const q = query(
      collection(db, COLLECTIONS.APPOINTMENTS),
      where('client', '==', clientId),
      where('caregiver', '==', caregiverId),
      where('status', '==', 'SCHEDULED')
    );

    const snapshot = await getDocs(q);

    // Check for exact time matches on the same date
    const exactTimeDuplicates = snapshot.docs
      .map(doc => ({ id: doc.id, ...doc.data() }))
      .filter(appointment => {
        // Exclude the current appointment if editing
        if (excludeAppointmentId && appointment.id === excludeAppointmentId) {
          return false;
        }

        const existingDate = moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('YYYY-MM-DD');
        const existingStartTime = moment(appointment.startDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm');
        const existingEndTime = moment(appointment.endDateTime, 'YYYY-MM-DD HH:mm').format('HH:mm');

        return existingDate === date && existingStartTime === startTime && existingEndTime === endTime;
      });

    return exactTimeDuplicates.length > 0;
  } catch (error) {
    console.error('Error checking for exact time duplicate:', error);
    // Return false to allow appointment creation if check fails
    return false;
  }
};


