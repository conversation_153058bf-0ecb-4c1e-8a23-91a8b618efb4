import { ref, uploadString, getDownloadURL, deleteObject, uploadBytes, getMetadata } from "firebase/storage";
import { storage } from "../config/firebase.config";
import { v4 as uuid } from "uuid";

// UPLOAD IMAGE TO FIREBASE (ACCEPT AS DATA URL)
async function uploadImageToFirebase(folder, file, fileName) {
  const str = fileName ? fileName : uuid();
  const uploadRef = ref(storage, `${folder}/${str}`);
  const uploadedImageRef = await uploadString(uploadRef, file, "data_url");
  const url = await getDownloadURL(uploadedImageRef.ref);
  return { url, path: uploadedImageRef.ref.fullPath };
}

// UPLOAD FILE TO FIREBASE (ACCEPT AS BUFFER OR BLOB)
async function uploadFileToFirebase(folder, file, fileExtension, fileName) {
  const str = fileName ? fileName : uuid();
  const uploadRef = ref(storage, `${folder}/${str}${fileName ? "" : `.${fileExtension}`}`);
  const uploadedFileRef = await uploadBytes(uploadRef, file, { cacheControl: "no-store" });
  const url = await getDownloadURL(uploadedFileRef.ref);
  return { url, path: uploadedFileRef.ref.fullPath };
}

// GET FILE SIZE UPLOADED TO FIREBASE VIA URL
async function getFirebaseFileSize(url) {
  const fileRef = ref(storage, url);
  const metadata = await getMetadata(fileRef);
  return metadata.size;
}

// DELETE IMAGE FILE
const deleteFileFromStorage = async (url) => {
  try {
    const oldRef = ref(storage, url);
    const downloadURL = await getDownloadURL(oldRef);
    if (downloadURL) {
      await deleteObject(oldRef);
    }
  } catch (error) {
    console.log("Error deleting storage object", error);
  }
};

export { uploadImageToFirebase, uploadFileToFirebase, deleteFileFromStorage, getFirebaseFileSize };
