// styling
import styled from "styled-components/macro";
import { flex, breakpoints } from "@styles/vars";

// styled components
import { Header } from "@components/Widget/style";

// components
import Widget from "@components/Widget";
import WidgetBody from "@components/Widget/WidgetBody";
import CustomSelect from "@ui/Select";
import GenderNav from "@components/GenderNav";
import SearchBar from "@ui/SearchBar";
import Group from "./Group";
import NoDataPlaceholder from "@components/NoDataPlaceholder";

// utils
import { depsOptions } from "@constants/options";
import { useState } from "react";

// hooks
import useGenderFilter from "@hooks/useGenderFilter";

// data placeholder
import { caregivers } from "@db/staff";
import { useSelector } from "react-redux";
import Item from "@components/PersonList/Item";

export const ListHeader = styled(Header)`
  padding: 24px 0 20px;

  .wrapper {
    padding: 0 24px;
    ${flex.col};
    gap: 20px;
  }

  .wrapper,
  form {
    flex-grow: 1;
    width: 100%;
  }

  ${breakpoints.tablet} {
    .wrapper {
      flex-direction: row;
      ${flex.between};

      .gender {
        width: 300px;
      }
    }
  }
`;

const NursesList = ({ variant }) => {
  const { nurses } = useSelector((state) => state.users);

  const [category, setCategory] = useState(depsOptions[0]);
  const [search, setSearch] = useState("");
  const { gender, setGender } = useGenderFilter();

  const filteredNurses = nurses?.filter((item) => {
    const name = `${item.name}`;
    const depsNames = item?.department?.map((dep) => dep?.label).join(" ");
    const depsIDs = item?.department?.map((dep) => dep?.id).join(" ");
    const queryMatch =
      name?.toLowerCase()?.includes(search?.toLowerCase()) || depsNames?.toLowerCase()?.includes(search?.toLowerCase());
    const categoryMatch = category?.value === "all" || depsIDs?.toLowerCase().includes(category?.value.toLowerCase());
    const genderMatch = gender?.value === "all" || item?.gender === gender?.value;

    return queryMatch && categoryMatch && genderMatch;
  });

  return (
    <Widget name="NursesList">
      <ListHeader>
        <div className="wrapper">
          <CustomSelect options={depsOptions} variant="minimal" value={category} changeHandler={setCategory} />
          <GenderNav state={gender} handler={setGender} />
        </div>
        <SearchBar placeholder="Search a nurse or medical department" handler={setSearch} value={search} />
      </ListHeader>
      <WidgetBody style={{ padding: 0 }}>
        {filteredNurses.length !== 0 ? (
          <Group
            arr={filteredNurses}
            variant={variant}
            gender={gender.value}
            deps={{
              category: category.value,
              search: search,
            }}
          />
        ) : (
          <NoDataPlaceholder />
        )}
      </WidgetBody>

      {/* {filtered_nurses()?.length > 0 ? (
          filtered_nurses().map((item) => (
            <Item
              key={item.id}
              data={{
                firstName: item.name,
                lastName: "",
                avatar: item?.photo,
                ...item,
              }}
              type={"nurse"}
            />
          ))
        ) : (
          <NoDataPlaceholder />
        )} */}
    </Widget>
  );
};

export default NursesList;
