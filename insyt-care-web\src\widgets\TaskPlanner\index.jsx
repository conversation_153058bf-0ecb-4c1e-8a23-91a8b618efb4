// styled components
import { AddButton } from "@widgets/DailyPlanner/style";

// components
import Widget from "@components/Widget";
import WidgetHeader from "@components/Widget/WidgetHeader";
import WidgetBody from "@components/Widget/WidgetBody";
import AddForm from "@components/Todos/AddForm";
import TodosLegend from "@components/Todos/TodosLegend";
import DnDLayout from "@components/Todos/DnDLayout";
import ScrollContainer from "@components/ScrollContainer";

// hooks
import { useState, useRef, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import useContentHeight from "@hooks/useContentHeight";
import NoDataPlaceholder from "@components/NoDataPlaceholder";
import { nanoid } from "nanoid";
import Btn from "@ui/Btn";
import { doc, setDoc, updateDoc } from "firebase/firestore";
import { db } from "@config/firebase.config";
import { COLLECTIONS } from "@constants/app";
import { useSnackbar } from "notistack";
import { addNewTaskDocAction } from "@store/slices/tasks";

const DailyPlanner = () => {
  const todos = useSelector((state) => state["todos"].todos);
  const { tasks, currentPatient, currentCaregiver, currentNurse } = useSelector((state) => state.tasks);
  const [isLoading, setLoading] = useState(false);
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();

  const [isFormVisible, setFormVisible] = useState(false);

  const headerRef = useRef(null);
  const footerRef = useRef(null);
  const listRef = useRef(null);
  const height = useContentHeight(headerRef, footerRef);

  useEffect(() => {
    listRef.current.scrollTo(0, 0);
  }, [isFormVisible]);

  return (
    <Widget name="DailyPlanner" mobile={600}>
      <WidgetHeader title="Tasks List" style={{ paddingBottom: 18 }} elRef={headerRef}>
        <AddButton
          onClick={() => setFormVisible(true)}
          className={isFormVisible ? "disabled" : ""}
          aria-label="Add new task"
        >
          +
        </AddButton>
      </WidgetHeader>
      <WidgetBody>
        <ScrollContainer height={height}>
          <div className="track" ref={listRef} style={{ overflowY: isFormVisible ? "hidden" : "auto" }}>
            <AddForm isVisible={isFormVisible} visibilityHandler={setFormVisible} />
            {tasks?.length ? <DnDLayout variant="planner" tasks={tasks} /> : <NoDataPlaceholder />}
          </div>
        </ScrollContainer>
        <div ref={footerRef} style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          {/* <TodosLegend /> */}
          {/* {tasksDetails && (
            <div style={{ width: "100px" }}>
              <Btn
                text="Save"
                type="button"
                className={isLoading ? "disabled" : "success"}
                handler={onUpdateTaskDetails}
              />
            </div>
          )} */}
        </div>
      </WidgetBody>
    </Widget>
  );
};

export default DailyPlanner;
