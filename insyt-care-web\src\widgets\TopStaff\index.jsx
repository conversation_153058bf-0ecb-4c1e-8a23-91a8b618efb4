import React from "react";
import Widget from "@components/Widget";
import WidgetHeader from "@components/Widget/WidgetHeader";
import useContentHeight from "@hooks/useContentHeight";
import { useRef } from "react";
import WidgetBody from "@components/Widget/WidgetBody";
import ScrollContainer from "@components/ScrollContainer";
import { Box, Typography } from "@mui/material";
import Avatar from "@ui/Avatar";
import { theme as muiTheme } from "@styles/mui-theme";
import theme from "styled-theming";
import { colors } from "@styles/vars";
import styled from "styled-components";
import { useSelector } from "react-redux";
import NoDataPlaceholder from "@components/NoDataPlaceholder";
import { getNameInitials } from "@utils/helpers";
import { useNavigate } from 'react-router-dom';

const acitveStaffBg = theme("theme", {
  light: "rgba(0, 137, 140, 0.2)",
  dark: "rgba(0, 137, 140, 0.2)",
});

const acitveStaffText = theme("theme", {
  light: "rgba(0, 137, 140, 1)",
  dark: "rgba(0, 137, 140, 1)",
});

const inacitveStaffText = theme("theme", {
  light: "rgb(140, 0, 0)",
  dark: "rgb(140, 0, 0)",
});

const inacitveStaffBg = theme("theme", {
  light: "rgba(140, 0, 0, 0.2)",
  dark: "rgba(138, 30, 30, 0.2)",
});

const StaffStatus = styled.p`
  color: ${(props) => (props?.active ? acitveStaffText : inacitveStaffText)};
  background-color: ${(props) => (props?.active ? acitveStaffBg : inacitveStaffBg)};
  padding: 2px 8px;
  font-size: 12px;
  border-radius: 99px;
`;

const TopStaff = () => {
  const headerRef = useRef(null);
  const height = useContentHeight(headerRef);

  const { user } = useSelector((state) => state.auth);
  const { appointments } = useSelector((state) => state.appointments);
  const { nurses, caregivers } = useSelector((state) => state.users);
const navigate = useNavigate();
  const getStaffAppointmentCounts = () => {
    const staffCounts = {};

    // Combine nurses and caregivers
    const allStaff = user?.role === "ADMIN" ? [...nurses, ...caregivers] : [...caregivers];

    // Count appointments for each staff member
    appointments.forEach((appointment) => {
      if (appointment?.nurse && user?.role === "ADMIN") {
        staffCounts[appointment?.nurse] = (staffCounts[appointment?.nurse] || 0) + 1;
      }
      if (appointment.caregiver) {
        staffCounts[appointment?.caregiver] = (staffCounts[appointment?.caregiver] || 0) + 1;
      }
    });
     
    // Convert to array of objects
    return Object.entries(staffCounts)
      .map(([staffId, count]) => ({
        staffId,
        appointmentCount: count,
        staffMember: allStaff.find((staff) => staff?.id === staffId),
      }))
      .sort((a, b) => b.appointmentCount - a.appointmentCount)
      .slice(0, 5);
  };

  // Function to navigate to staff detail page
  const handleStaffClick = (staffId) => {
    navigate(`/staff/${staffId}`);
  };

  const staffStats = getStaffAppointmentCounts();

  return (
    <>
      <Widget name="TopStaff" mobile={500}>
        <WidgetHeader title="Top Staff" style={{ paddingBottom: 19 }} flex="column" elRef={headerRef}></WidgetHeader>
        <WidgetBody>
          <ScrollContainer height={height}>
            <div className="track">
              {staffStats.length > 0 ? (
                staffStats.map((staff, index) => (
                  <TopStaffItem
                  className='top-staff'
                    key={index}
                    staffMember={staff?.staffMember}
                    appointmentCount={staff.appointmentCount}
                    onClick={() => handleStaffClick(staff?.staffMember?.id)}
                   
                  />
                ))
              ) : (
                <NoDataPlaceholder />
              )}
            </div>
          </ScrollContainer>
        </WidgetBody>
      </Widget>
    </>
  );
};

export default TopStaff;

const TopStaffItem = ({ appointmentCount, staffMember, onClick }) => {
  return (
    <>
      <Box
        className="top-staff-item"
        display="flex"
        flexDirection="column"
        alignItems="center"
        gap={"12px"}
        sx={{
          borderBottom: "1px dashed rgba(212, 212, 212, 1)",
          py: 3,
          "&.top-staff-item:first-of-type": { paddingTop: 0 },
          "&.top-staff-item:last-of-type": { border: "none", paddingBottom: 0 },
        }}
        onClick={onClick} // Add onClick prop to Box
      >
        <Box display="flex" flexDirection="column" alignItems="center" gap={"5px"} mb={"0px"}>
          <Avatar
            avatar={{ jpg: staffMember?.photo?.url || "" }}
            initals={getNameInitials(staffMember?.firstName, staffMember?.lastName)}
          />
          <Typography fontWeight={500}>{staffMember?.name}</Typography>
          <Typography color={muiTheme.palette.primary.main} fontSize={14}>
            {staffMember?.email}
          </Typography>
        </Box>

        <Box display="flex" justifyContent="space-between" alignItems="center" width="100%">
          <Typography color={muiTheme.palette.grey[500]} fontSize={14}>
            Appointments
          </Typography>
          <Typography fontSize={14} fontWeight={500}>
            {appointmentCount}
          </Typography>
        </Box>
        {/* <Box display="flex" justifyContent="space-between" alignItems="center" width="100%">
          <Typography color={muiTheme.palette.grey[500]} fontSize={14}>
            Status
          </Typography>
          <StaffStatus active={Boolean(staffMember?.isActive)}>
            {Boolean(staffMember?.isActive) ? "Active" : "Inactive"}
          </StaffStatus>
        </Box> */}
      </Box>
    </>
  );
};
